#!/usr/bin/env python3
"""
LM Studio API 連接測試腳本
"""
import requests
import json
import sys

# LM Studio 配置
BASE_URL = "http://10.0.4.86:1234"
MODEL = "google/gemma-3-4b"

def test_models_endpoint():
    """測試 /v1/models 端點"""
    print("🔍 測試 Models 端點...")
    try:
        response = requests.get(f"{BASE_URL}/v1/models", timeout=10)
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            models = response.json()
            print("✅ Models 端點連接成功！")
            print(f"可用模型數量: {len(models.get('data', []))}")
            
            for model in models.get('data', []):
                print(f"  - {model.get('id', 'Unknown')}")
            
            return True
        else:
            print(f"❌ Models 端點失敗: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到 LM Studio，請檢查：")
        print(f"   1. LM Studio 是否在 {BASE_URL} 運行")
        print("   2. 網路連接是否正常")
        print("   3. 防火牆設定")
        return False
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_chat_completion():
    """測試文字聊天完成"""
    print("\n💬 測試 Chat Completion 端點...")
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user", 
                "content": "請簡單回答：你好，你是什麼模型？"
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            json=payload,
            timeout=30
        )
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print("✅ Chat Completion 成功！")
            print(f"AI 回應: {content}")
            return True
        else:
            print(f"❌ Chat Completion 失敗: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

def test_vision_api():
    """測試視覺 API（如果支援）"""
    print("\n👁 測試 Vision API...")
    
    # 創建一個簡單的測試圖像 Base64（1x1 白色像素 PNG）
    test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "請描述這張圖片的內容"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{test_image_base64}"
                        }
                    }
                ]
            }
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            json=payload,
            timeout=30
        )
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print("✅ Vision API 支援！")
            print(f"圖像分析結果: {content}")
            return True
        else:
            print(f"⚠️  Vision API 可能不支援或模型不支援視覺功能")
            print(f"回應: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Vision 測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 LM Studio API 連接測試")
    print(f"目標端點: {BASE_URL}")
    print(f"測試模型: {MODEL}")
    print("=" * 50)
    
    # 測試步驟
    models_ok = test_models_endpoint()
    
    if models_ok:
        chat_ok = test_chat_completion()
        vision_ok = test_vision_api()
        
        print("\n" + "=" * 50)
        print("📊 測試結果摘要:")
        print(f"  Models API: {'✅' if models_ok else '❌'}")
        print(f"  Chat API: {'✅' if chat_ok else '❌'}")  
        print(f"  Vision API: {'✅' if vision_ok else '⚠️ (可能不支援)'}")
        
        if models_ok and chat_ok:
            print("\n🎉 LM Studio API 基本功能測試通過！")
            print("影片分析工具可以正常使用文字分析功能。")
            
            if vision_ok:
                print("🖼️ 視覺分析功能也可用於截圖分析！")
            else:
                print("⚠️ 視覺功能測試失敗，可能需要支援多模態的模型。")
        else:
            print("\n❌ 基本 API 測試失敗，請檢查 LM Studio 設定。")
    else:
        print("\n❌ 無法連接到 LM Studio，請先解決連接問題。")

if __name__ == "__main__":
    main()