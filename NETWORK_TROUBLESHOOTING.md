# LM Studio 網路連接疑難排解

## 🔧 當前狀態

**配置的 LM Studio 端點**: `http://***********:1234`  
**模型**: `google/gemma-3-4b`

## ❌ 診斷結果

```bash
# 網路連接測試
$ ping ***********
PING ***********: 100.0% packet loss ❌

# API 連接測試
$ curl http://***********:1234/v1/models
curl: (28) Timeout ❌
```

## 🔍 可能的問題和解決方案

### 1. **檢查 LM Studio 是否運行**
```bash
# 在 LM Studio 主機上檢查服務狀態
netstat -an | grep 1234
# 或
lsof -i :1234
```

**LM Studio 設定檢查清單**:
- ✅ LM Studio 應用程式已啟動
- ✅ 模型已載入 (`google/gemma-3-4b`)
- ✅ 本地伺服器已啟動（通常在 "Local Server" 標籤）
- ✅ 伺服器監聽 `0.0.0.0:1234`（不是 `127.0.0.1:1234`）

### 2. **網路連接問題**
```bash
# 檢查網路路由
traceroute ***********

# 檢查網路介面
ipconfig getifaddr en0    # macOS
ip addr show             # Linux
```

**網路檢查清單**:
- ✅ 兩台設備在同一網路段
- ✅ 無 VPN 或代理干擾
- ✅ WiFi/網路連接穩定

### 3. **防火牆設定**

**在 LM Studio 主機上**:
```bash
# macOS 防火牆
sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate

# Linux 防火牆 (ufw)
sudo ufw status

# Windows 防火牆
netsh advfirewall show allprofiles state
```

**防火牆設定**:
- ✅ 允許 port 1234 入站連接
- ✅ 允許 LM Studio 應用程式網路存取
- ✅ 企業防火牆不阻擋內網連接

### 4. **LM Studio 配置**

**在 LM Studio 中檢查**:
1. 開啟 "Local Server" 標籤
2. 確認 Server 設定:
   ```
   Host: 0.0.0.0 (不是 localhost 或 127.0.0.1)
   Port: 1234
   Model: google/gemma-3-4b
   ```
3. 點擊 "Start Server"
4. 確認狀態顯示 "Server running"

## 🛠 測試步驟

### 第一步：在 LM Studio 主機上本地測試
```bash
# 在 *********** 的機器上執行
curl http://localhost:1234/v1/models

# 應該返回類似：
# {
#   "data": [
#     {"id": "google/gemma-3-4b", ...}
#   ]
# }
```

### 第二步：測試網路連接
```bash
# 從我們的機器測試
telnet *********** 1234

# 或使用 nc (netcat)
nc -zv *********** 1234
```

### 第三步：使用我們的測試工具
```bash
# 使用 .NET 測試工具
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api

# 或使用 Python 測試腳本（如果可用）
python3 test-lm-studio.py
```

## 🌐 替代方案

### 選項 1: 本地運行 LM Studio
如果無法連接到遠端 LM Studio，可以在本機安裝：

```yaml
# 修改 appsettings.yaml
LMStudio:
  BaseUrl: http://localhost:1234
```

### 選項 2: 使用不同的 AI 服務
考慮使用其他 AI API：
- OpenAI API
- Anthropic Claude API
- Google Vertex AI
- Ollama (本地運行)

### 選項 3: SSH 隧道
如果有 SSH 存取權限：
```bash
ssh -L 1234:localhost:1234 user@***********
```

然後使用：
```yaml
LMStudio:
  BaseUrl: http://localhost:1234
```

## 📞 需要更多資訊

請提供以下資訊以進一步診斷：

1. **LM Studio 主機資訊**:
   - 作業系統？(Windows/macOS/Linux)
   - LM Studio 版本？
   - 是否已載入 `google/gemma-3-4b` 模型？

2. **網路環境**:
   - 兩台設備是否在同一 WiFi/網路？
   - 是否使用 VPN？
   - 是否在企業網路環境？

3. **LM Studio 設定截圖**:
   - Local Server 設定畫面
   - 模型載入狀態
   - 伺服器運行狀態

## 🧪 快速驗證指令

執行這個一鍵測試來快速診斷問題：

```bash
echo "=== LM Studio 連接診斷 ==="
echo "1. 網路連接測試:"
ping -c 3 *********** 2>/dev/null && echo "✅ 網路可達" || echo "❌ 網路不可達"

echo "2. Port 連接測試:"
nc -zv *********** 1234 2>/dev/null && echo "✅ Port 1234 開放" || echo "❌ Port 1234 關閉或被阻擋"

echo "3. HTTP 測試:"
curl -s --connect-timeout 5 http://***********:1234/v1/models >/dev/null && echo "✅ HTTP API 可用" || echo "❌ HTTP API 無法連接"

echo "4. .NET 測試 (如果已編譯):"
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api
```