{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/src/VideoContentAnalyzer.Console/VideoContentAnalyzer.Console.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/src/VideoContentAnalyzer.Console/VideoContentAnalyzer.Console.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/src/VideoContentAnalyzer.Console/VideoContentAnalyzer.Console.csproj"], "group": "build", "presentation": {"reveal": "always"}, "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/src/VideoContentAnalyzer.Console/VideoContentAnalyzer.Console.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore"], "group": "build", "presentation": {"reveal": "silent"}, "problemMatcher": []}]}