{"mcpServers": {"github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "webresearch": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/mcp-webresearch/dist/index.js"], "env": {}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>"], "env": {}}, "think": {"command": "/Users/<USER>/.local/pipx/venvs/pip/bin/mcp-think-tool", "args": [], "type": "stdio", "pollingInterval": 30000, "startupTimeout": 30000, "restartOnFailure": true}, "desktop-commander": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/DesktopCommanderMCP/dist/index.js", "--allowed-dir=/Users/<USER>/opensource", "--allowed-dir=/Users/<USER>/doc", "--allowed-dir=/Users/<USER>/RiderProjects", "--allowed-dir=/Users/<USER>/RiderProjects/google-oauth-demo", "--allowed-dir=/Users/<USER>/Documents/Cline/MCP", "--allowed-dir=/Users/<USER>/.mcp", "--allowed-dir=/Users/<USER>/lab", "--allowed-dir=/Users/<USER>/RiderProjects/mcp-google-calendar-booking"]}, "playwright": {"command": "node", "args": ["/Users/<USER>/Documents/Cline/MCP/mcp-playwright/dist/index.js"]}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAjaNOWqrz2SAWcVbUz1-bVgut1DZZ"}}, "grafana": {"command": "/Users/<USER>/Documents/Cline/MCP/mcp-grafana/dist/mcp-grafana", "args": [], "env": {"GRAFANA_URL": "http://localhost:3000", "GRAFANA_API_KEY": "glsa_OYeHmUODKT93ivatulALkOMh2sU7zJVd_d6f33f58"}}, "kubernetes": {"command": "npx", "args": ["-y", "kubernetes-mcp-server@latest"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"OPENROUTER_API_KEY": "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"}}, "postgres": {"command": "postgres-mcp", "args": ["--access-mode=unrestricted"], "env": {"DATABASE_URI": "***********************************************************************************"}}, "mssql": {"command": "node", "args": ["/Users/<USER>/opensource/SQL-AI-samples/MssqlMcp/Node/dist/index.js"], "env": {"SERVER_NAME": "", "DATABASE_NAME": "DemoApp", "READONLY": "false"}}, "serena": {"type": "stdio", "command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena", "start-mcp-server", "--context", "ide-assistant", "--project", "/Users/<USER>/RiderProjects/video-content-analyze"], "env": {}}}}