# 任務完成檢查清單

## 開發任務完成後應執行的檢查

### 1. 建置驗證
```bash
# 確保專案能成功建置
dotnet build

# 檢查是否有編譯警告
dotnet build --verbosity normal
```

### 2. 配置檔案檢查
- 確認 `appsettings.yaml` 存在且配置正確
- 檢查 LM Studio 端點設定
- 驗證 Whisper 模型路徑
- 確認外部相依性路徑

### 3. 功能測試
```bash
# 測試 LM Studio API 連接
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api

# 測試基本影片分析功能
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/test-video.mp4 --verbose
```

### 4. 外部相依性檢查
- **LM Studio**: 確認服務正在運行 (預設 http://localhost:1234)
- **FFmpeg**: 驗證安裝且在 PATH 中可用
- **Whisper 模型**: 確認模型檔案存在於指定路徑
- **yt-dlp**: 如使用 YouTube 功能，確認已安裝

### 5. 日誌和錯誤處理
- 檢查是否有適當的錯誤日誌記錄
- 確認例外處理不會中斷主要流程
- 驗證進度報告功能正常

### 6. 程式碼品質檢查
目前專案沒有自動化的程式碼品質工具，但應檢查：
- 是否遵循專案命名慣例
- 非同步方法是否正確實作
- 是否適當使用相依性注入
- 配置是否透過 Options Pattern 管理

### 7. 效能考量
- 檢查並發處理是否合理 (預設批次大小為 5)
- 確認記憶體使用合理 (特別是圖像處理)
- 驗證暫存檔案清理機制

### 8. 使用者體驗
- 測試命令列參數是否正確解析
- 確認進度顯示和狀態訊息清晰
- 驗證錯誤訊息對使用者友善

## 特別注意事項
- 由於整合多個外部服務 (LM Studio, FFmpeg, Whisper)，確保錯誤處理涵蓋各種失敗情況
- 測試在沒有某些外部相依性時的優雅降級行為
- 確認配置檔案中的路徑在不同作業系統下正確解析