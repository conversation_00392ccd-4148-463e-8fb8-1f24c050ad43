# 程式碼風格和慣例

## 專案風格特點

### .NET 相關
- **目標框架**: .NET 9
- **語言特性**: 啟用 ImplicitUsings 和 Nullable 引用類型
- **專案結構**: Clean Architecture 三層分離

### 命名慣例
基於程式碼觀察到的慣例：

#### 類別和介面
- 類別使用 PascalCase: `VideoAnalyzer`, `LMStudioClient`
- 介面使用 I 前綴: `IVideoAnalyzer`, `IAIAnalysisService`
- 服務類別通常以 Service 結尾: `AIAnalysisService`, `SubtitleService`

#### 方法和屬性
- 方法使用 PascalCase: `AnalyzeVideoAsync`, `ExtractFramesAsync`
- 非同步方法以 Async 結尾
- 私有欄位使用底線前綴: `_logger`, `_aiAnalysisService`

#### 檔案和目錄
- 檔案名稱與類別名稱相同
- 目錄使用 PascalCase: `Models`, `Services`, `Interfaces`

### 程式碼組織
- **相依性注入**: 使用 Microsoft.Extensions.DependencyInjection
- **配置管理**: YAML 配置檔案 (appsettings.yaml)
- **日誌記錄**: 使用 Microsoft.Extensions.Logging
- **例外處理**: 結構化例外處理，記錄但不中斷整體流程

### 非同步程式設計
- 廣泛使用 async/await 模式
- 方法接受 CancellationToken 參數
- 非同步方法名稱以 Async 結尾

### 進度報告
- 實作 IProgress<T> 介面用於進度追蹤
- 使用 AnalysisProgress 模型回報處理狀態

### 配置選項模式
使用 Options Pattern：
- 配置類別: `LMStudioOptions`, `WhisperOptions` 等
- 透過 IOptions<T> 注入配置

### UI 和使用者體驗
- 使用 Spectre.Console 提供豐富的命令列介面
- 包含進度條、表格、顏色標記等視覺元素
- 支援多語言輸出 (主要為繁體中文)

### 錯誤處理策略
- 個別項目失敗不中斷整體處理
- 詳細的錯誤日誌記錄
- 在摘要中標註失敗項目