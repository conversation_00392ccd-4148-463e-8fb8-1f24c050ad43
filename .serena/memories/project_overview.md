# Video Content Analyzer - 專案概述

## 專案目的
Video Content Analyzer 是一個基於 .NET 9 和 AI 技術的影片內容分析工具，能夠：
- 分析影片場景內容和活動
- 識別影片畫面中的文字內容（多語言支援）
- 生成字幕和語義分析
- 整合本地 AI 模型進行視覺分析

## 技術堆疊
- **.NET 9**: 主要開發框架
- **Clean Architecture**: 分層架構設計
- **LM Studio**: 本地 AI 模型推理伺服器
- **FFMpegCore**: 影片處理和幀擷取
- **Whisper.net**: 語音轉文字
- **RestSharp**: HTTP 客戶端
- **Spectre.Console**: 命令列介面
- **SixLabors.ImageSharp**: 圖像處理

## 架構概覽
採用 Clean Architecture 三層設計：

### Core Layer (VideoContentAnalyzer.Core)
- **Models/**: 核心資料模型 (VideoAnalysisResult, FrameAnalysis, SubtitleSegment 等)
- **Interfaces/**: 服務介面定義
- **Services/**: 核心業務邏輯 (VideoAnalyzer 主要分析流程)

### Infrastructure Layer (VideoContentAnalyzer.Infrastructure)
- **AI/**: LM Studio 客戶端和 AI 分析服務
- **Media/**: FFmpeg 影片處理和 YouTube 下載
- **Subtitle/**: 字幕解析和生成服務

### Application Layer (VideoContentAnalyzer.Console)
- CLI 應用程式入口點
- 依賴注入配置
- 命令列參數處理

## 外部相依性
- **LM Studio**: 本地運行的視覺語言模型伺服器
- **FFmpeg**: 系統層級影片處理工具
- **Whisper 模型**: 語音識別模型檔案
- **yt-dlp**: YouTube 影片下載工具

## 特殊功能
### Place Recognition
專門針對餐廳和景點識別的功能：
- 多語言文字識別（中文、日文、韓文）
- 結構化場所資訊提取
- 智慧摘要生成優先列出識別到的場所

### YouTube Integration
- 支援 YouTube URL 和 Video ID 輸入
- 自動下載多語言字幕
- 品質控制和自動清理功能