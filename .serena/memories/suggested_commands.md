# 建議的開發命令

## 基本開發命令

### 專案建置和恢復
```bash
dotnet restore                           # 恢復專案相依性
dotnet build                            # 建置整個解決方案
dotnet build --configuration Release   # Release 模式建置
dotnet clean                            # 清理建置輸出
```

### 執行應用程式
```bash
# 基本影片分析
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/video.mp4

# YouTube 影片分析
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube "https://www.youtube.com/watch?v=VIDEO_ID"
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube VIDEO_ID

# 測試 LM Studio API 連接
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api

# 進階參數使用
dotnet run --project src/VideoContentAnalyzer.Console -- \
  /path/to/video.mp4 \
  --interval 10 \
  --max-frames 30 \
  --output markdown \
  --verbose
```

### 開發和除錯
```bash
# 監視模式 (自動重新建置)
dotnet watch run --project src/VideoContentAnalyzer.Console

# 發布應用程式
dotnet publish src/VideoContentAnalyzer.Console --configuration Release
```

## 測試相關
目前專案沒有正式的單元測試專案，可用：
```bash
# 測試 LM Studio 連接
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api

# Python 測試腳本
python3 test-lm-studio.py
```

## 配置管理
```bash
# 複製配置範例檔案
cp appsettings.example.yaml appsettings.yaml

# 編輯配置檔案 (使用你偏好的編輯器)
nano appsettings.yaml
code appsettings.yaml
```

## VS Code 任務
專案包含 `.vscode/tasks.json` 定義的任務：
- `build`: 建置專案
- `clean`: 清理專案
- `restore`: 恢復相依性
- `watch`: 監視模式執行
- `publish`: 發布專案

## 環境變數
```bash
# YouTube API Key (如果使用 YouTube 功能)
export YOUTUBE_API_KEY=your_api_key_here

# 然後執行分析
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube VIDEO_ID
```