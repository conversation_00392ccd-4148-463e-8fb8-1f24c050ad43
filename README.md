# Video Content Analyzer

一個使用 .NET 9 和 AI 技術的影片場景分析工具，能夠分析影片內容、識別文字並生成詳細的場景描述。

## 功能特色

🎥 **影片場景分析**
- 自動擷取影片關鍵幀
- 使用 AI 分析場景內容和活動
- 智能場景變化偵測

🔤 **視覺文字識別**
- 檢測影片畫面中的文字內容
- 支援多語言文字識別
- 提供文字位置和信心度資訊

📝 **字幕內容分析**
- 支援 SRT、VTT、ASS 等字幕格式
- 使用 Whisper 自動生成字幕
- 字幕語義分析和關鍵詞萃取

🤖 **LM Studio 整合**
- 本地運行的 AI 模型支援
- 支援視覺語言模型（如 LLaVA）
- 可配置的 API 端點和模型設定

## 技術架構

```
VideoContentAnalyzer/
├── VideoContentAnalyzer.Core/        # 核心業務邏輯
│   ├── Models/                       # 資料模型
│   ├── Interfaces/                   # 服務介面
│   └── Services/                     # 核心服務
├── VideoContentAnalyzer.Infrastructure/ # 基礎設施層
│   ├── AI/                          # LM Studio 和 AI 服務
│   ├── Media/                       # 影片處理服務
│   └── Subtitle/                    # 字幕處理服務
└── VideoContentAnalyzer.Console/     # 控制台應用程式
```

## 系統需求

- .NET 9 或更高版本
- FFmpeg（影片處理）
- LM Studio（本地 AI 推理）
- Whisper 模型（語音轉文字）

## 安裝步驟

### 1. 安裝 .NET 9
```bash
# 下載並安裝 .NET 9 SDK
# https://dotnet.microsoft.com/download/dotnet/9.0
```

### 2. 安裝 FFmpeg
```bash
# macOS
brew install ffmpeg

# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# Windows
# 下載 FFmpeg 並添加到 PATH
```

### 3. 設定 LM Studio
```bash
# 1. 下載並安裝 LM Studio: https://lmstudio.ai/
# 2. 下載視覺語言模型（推薦 LLaVA）
# 3. 啟動本地伺服器（預設 http://localhost:1234）
```

### 4. 下載 Whisper 模型
```bash
# 建立 Whisper 模型目錄
mkdir -p ~/.whisper

# 下載模型（選擇適合的大小）
# base 模型：約 142MB，適合一般使用
wget https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.bin -O ~/.whisper/ggml-base.bin
```

### 5. 編譯專案
```bash
git clone <repository-url>
cd video-content-analyze
dotnet restore
dotnet build
```

## 使用方法

### 基本用法
```bash
# 分析影片（自動生成字幕）
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/video.mp4

# 使用現有字幕檔案
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/video.mp4 -s /path/to/subtitle.srt

# 指定輸出格式
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/video.mp4 -o markdown
```

### 進階參數
```bash
# 自訂分析參數
dotnet run --project src/VideoContentAnalyzer.Console -- \
  /path/to/video.mp4 \
  --interval 10 \           # 每10秒擷取一幀
  --max-frames 30 \         # 最多分析30幀
  --output json \           # JSON 格式輸出
  --verbose                 # 詳細日誌
```

### 可用選項
- `--subtitle, -s`: 字幕檔案路徑（可選）
- `--output, -o`: 輸出格式（json、markdown、text）
- `--interval, -i`: 幀擷取間隔秒數（預設：5）
- `--max-frames, -m`: 最大分析幀數（預設：50）
- `--no-subtitles`: 停用字幕生成
- `--verbose, -v`: 啟用詳細日誌

## 配置設定

編輯 `src/VideoContentAnalyzer.Console/appsettings.yaml`：

```yaml
# LM Studio Configuration
LMStudio:
  BaseUrl: http://localhost:1234
  VisionModel: llava-1.5-7b-q4_0
  TextModel: llama-2-7b-chat-q4_0
  MaxTokens: 1000
  Temperature: 0.7
  TimeoutSeconds: 120

# Whisper Configuration  
Whisper:
  ModelPath: ~/.whisper/ggml-base.bin
  ModelSize: base
  Language: auto
  Translate: false
  Threads: 4

# 截圖儲存配置
FrameExtraction:
  # 截圖儲存路徑 (支援 ~/ 和相對路徑)
  OutputDirectory: ./frames
  # 檔案名稱是否包含時間戳
  UseTimestampInFilename: true
  # 圖片格式 (jpg, png, bmp)
  ImageFormat: jpg
  # 圖片品質 (1-100)
  ImageQuality: 90
  # 分析完成後是否保留截圖
  KeepFrames: false
  # 是否為每個影片建立獨立目錄
  CreateSubDirectoryPerVideo: true
  # 子目錄命名格式
  SubDirectoryNameFormat: "{videoName}_{timestamp:yyyyMMdd_HHmmss}"
```

### 📁 截圖路徑配置說明

#### 可用的路徑選項：
- `./frames` - 當前目錄下的 frames 資料夾
- `~/VideoAnalysis/frames` - 使用者目錄下的 VideoAnalysis/frames
- `/tmp/video-frames` - 系統臨時目錄
- `C:\VideoFrames` - Windows 絕對路徑

#### 目錄結構範例：
```
frames/
├── video1_20241222_143022/     # 影片專用目錄
│   ├── frame_0000_5.00s.jpg   # 第5秒截圖
│   ├── frame_0001_10.00s.jpg  # 第10秒截圖
│   └── ...
└── video2_20241222_143055/
    ├── frame_0000_3.50s.jpg
    └── ...
```

#### 配置選項說明：
- **OutputDirectory**: 截圖主目錄路徑
- **UseTimestampInFilename**: 檔名中是否包含時間戳資訊
- **ImageFormat**: 圖片格式 (jpg/png/bmp)
- **ImageQuality**: JPEG 品質設定 (1-100)
- **KeepFrames**: 分析完成後是否保留截圖檔案
- **CreateSubDirectoryPerVideo**: 每個影片是否建立獨立子目錄
- **SubDirectoryNameFormat**: 子目錄命名模式（支援 `{videoName}`, `{timestamp:格式}` 變數）

## 輸出範例

### JSON 格式
```json
{
  "VideoPath": "/path/to/video.mp4",
  "VideoDuration": "00:05:30",
  "FrameAnalyses": [...],
  "SubtitleSegments": [...],
  "Summary": {
    "OverallDescription": "這段影片展示了...",
    "MainActivities": ["講話", "演示", "展示螢幕"],
    "DetectedObjects": ["人物", "電腦", "螢幕"],
    "DetectedText": ["標題文字", "選單項目"]
  }
}
```

### Markdown 格式
```markdown
# 影片內容分析報告

**影片時長：** 00:05:30
**分析時間：** 2024-08-22 10:30:45

## 影片摘要
這段影片展示了軟體開發的基本流程...

## 主要活動
- 程式碼編寫
- 系統演示
- 功能測試

## 關鍵幀分析
### 00:30
畫面顯示程式碼編輯器，開發者正在編寫 Python 程式碼...
```

## 疑難排解

### LM Studio 連線問題
1. 確認 LM Studio 已啟動並在 localhost:1234 提供服務
2. 檢查防火牆設定
3. 確認已載入適合的視覺語言模型

### Whisper 模型問題
1. 確認模型檔案路徑正確
2. 檢查模型檔案完整性
3. 嘗試重新下載模型

### FFmpeg 問題
1. 確認 FFmpeg 已安裝並在 PATH 中
2. 檢查影片檔案格式支援
3. 確認檔案權限

## 支援的格式

### 影片格式
- MP4, AVI, MOV, MKV, WMV, FLV

### 字幕格式
- SRT（SubRip）
- VTT（WebVTT）
- ASS/SSA（Advanced SubStation Alpha）

## 授權

MIT License

## 貢獻

歡迎提交 Issue 和 Pull Request！

## 更新日誌

### v1.0.0
- 初始版本發布
- 支援基本影片分析功能
- LM Studio 整合
- Whisper 字幕生成
- 多格式輸出支援