using Microsoft.Extensions.Options;
using RestSharp;
using Spectre.Console;
using System.Text.Json;
using VideoContentAnalyzer.Infrastructure.AI;

namespace VideoContentAnalyzer.Console;

public static class LMStudioTester
{
    public static async Task<bool> TestLMStudioConnectionAsync(LMStudioOptions options)
    {
        AnsiConsole.MarkupLine("[bold]🚀 LM Studio API 連接測試[/]");
        AnsiConsole.MarkupLine($"目標端點: [green]{options.BaseUrl}[/]");
        AnsiConsole.MarkupLine($"測試模型: [green]{options.TextModel}[/]");
        AnsiConsole.WriteLine();

        var clientOptions = new RestClientOptions(options.BaseUrl)
        {
            Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds)
        };
        var client = new RestClient(clientOptions);
        
        // 測試 1: Models 端點
        var modelsTest = await TestModelsEndpoint(client);
        
        if (!modelsTest)
        {
            AnsiConsole.MarkupLine("[red]❌ 無法連接到 LM Studio，請檢查連接設定[/]");
            return false;
        }

        // 測試 2: Chat Completion
        var chatTest = await TestChatCompletion(client, options);
        
        // 測試 3: Vision API (如果支援)
        var visionTest = await TestVisionAPI(client, options);

        // 顯示結果摘要
        var table = new Table();
        table.AddColumn("測試項目");
        table.AddColumn("結果");
        table.AddColumn("說明");

        table.AddRow("Models API", modelsTest ? "[green]✅[/]" : "[red]❌[/]", 
            modelsTest ? "連接正常" : "連接失敗");
        
        table.AddRow("Chat API", chatTest ? "[green]✅[/]" : "[red]❌[/]", 
            chatTest ? "文字對話功能正常" : "文字對話失敗");
        
        table.AddRow("Vision API", visionTest ? "[green]✅[/]" : "[yellow]⚠️[/]", 
            visionTest ? "視覺分析功能可用" : "視覺功能不支援");

        AnsiConsole.Write(table);

        var overallSuccess = modelsTest && chatTest;
        
        if (overallSuccess)
        {
            AnsiConsole.MarkupLine("\n[green]🎉 LM Studio API 基本功能測試通過！[/]");
            AnsiConsole.MarkupLine("影片分析工具可以正常使用。");
            
            if (visionTest)
            {
                AnsiConsole.MarkupLine("[blue]🖼️ 視覺分析功能也可用於截圖分析！[/]");
            }
            else
            {
                AnsiConsole.MarkupLine("[yellow]⚠️ 視覺功能不可用，可能需要支援多模態的模型（如 LLaVA）[/]");
            }
        }
        else
        {
            AnsiConsole.MarkupLine("\n[red]❌ API 測試失敗，請檢查 LM Studio 設定[/]");
        }

        return overallSuccess;
    }

    private static async Task<bool> TestModelsEndpoint(RestClient client)
    {
        AnsiConsole.MarkupLine("🔍 測試 Models 端點...");
        
        try
        {
            var request = new RestRequest("/v1/models", Method.Get);
            var response = await client.ExecuteAsync(request);

            if (response.IsSuccessful && !string.IsNullOrEmpty(response.Content))
            {
                var modelsResponse = JsonSerializer.Deserialize<ModelsResponse>(response.Content);
                var modelCount = modelsResponse?.Data?.Count ?? 0;
                
                AnsiConsole.MarkupLine($"[green]✅ Models 端點連接成功！發現 {modelCount} 個可用模型[/]");
                
                if (modelsResponse?.Data != null)
                {
                    foreach (var model in modelsResponse.Data.Take(5)) // 只顯示前5個
                    {
                        AnsiConsole.MarkupLine($"  - {model.Id}");
                    }
                }
                
                return true;
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]❌ Models 端點失敗: {response.ErrorMessage}[/]");
                return false;
            }
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Models 端點測試失敗: {ex.Message}[/]");
            return false;
        }
    }

    private static async Task<bool> TestChatCompletion(RestClient client, LMStudioOptions options)
    {
        AnsiConsole.MarkupLine("💬 測試 Chat Completion 端點...");
        
        try
        {
            var request = new RestRequest("/v1/chat/completions", Method.Post);
            request.AddHeader("Content-Type", "application/json");

            var payload = new
            {
                model = options.TextModel,
                messages = new[]
                {
                    new { role = "user", content = "請簡單回答：你好，你是什麼模型？" }
                },
                max_tokens = 100,
                temperature = 0.7
            };

            request.AddJsonBody(payload);

            var response = await client.ExecuteAsync(request);

            if (response.IsSuccessful && !string.IsNullOrEmpty(response.Content))
            {
                var chatResponse = JsonSerializer.Deserialize<ChatCompletionResponse>(response.Content);
                var content = chatResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "";
                
                AnsiConsole.MarkupLine("[green]✅ Chat Completion 成功！[/]");
                AnsiConsole.MarkupLine($"AI 回應: [italic]{content.Trim()}[/]");
                return true;
            }
            else
            {
                AnsiConsole.MarkupLine($"[red]❌ Chat Completion 失敗: {response.ErrorMessage}[/]");
                AnsiConsole.MarkupLine($"回應內容: {response.Content}");
                return false;
            }
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[red]❌ Chat Completion 測試失敗: {ex.Message}[/]");
            return false;
        }
    }

    private static async Task<bool> TestVisionAPI(RestClient client, LMStudioOptions options)
    {
        AnsiConsole.MarkupLine("👁 測試 Vision API...");
        
        try
        {
            var request = new RestRequest("/v1/chat/completions", Method.Post);
            request.AddHeader("Content-Type", "application/json");

            // 1x1 白色像素的 PNG 圖片 Base64
            var testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==";

            var payload = new
            {
                model = options.VisionModel,
                messages = new[]
                {
                    new
                    {
                        role = "user",
                        content = new object[]
                        {
                            new { type = "text", text = "請描述這張圖片" },
                            new
                            {
                                type = "image_url",
                                image_url = new { url = $"data:image/png;base64,{testImageBase64}" }
                            }
                        }
                    }
                },
                max_tokens = 100,
                temperature = 0.7
            };

            request.AddJsonBody(payload);

            var response = await client.ExecuteAsync(request);

            if (response.IsSuccessful && !string.IsNullOrEmpty(response.Content))
            {
                var visionResponse = JsonSerializer.Deserialize<ChatCompletionResponse>(response.Content);
                var content = visionResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "";
                
                AnsiConsole.MarkupLine("[green]✅ Vision API 支援！[/]");
                AnsiConsole.MarkupLine($"圖像分析結果: [italic]{content.Trim()}[/]");
                return true;
            }
            else
            {
                AnsiConsole.MarkupLine("[yellow]⚠️ Vision API 可能不支援或模型不支援視覺功能[/]");
                return false;
            }
        }
        catch (Exception ex)
        {
            AnsiConsole.MarkupLine($"[yellow]⚠️ Vision API 測試失敗: {ex.Message}[/]");
            return false;
        }
    }
}

// JSON 回應模型
public class ModelsResponse
{
    public List<ModelInfo>? Data { get; set; }
}

public class ModelInfo
{
    public string Id { get; set; } = "";
}

public class ChatCompletionResponse
{
    public List<Choice>? Choices { get; set; }
}

public class Choice
{
    public Message? Message { get; set; }
}

public class Message
{
    public string? Content { get; set; }
}