using FFMpegCore;
using FFMpegCore.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.Media;

public class VideoFrameExtractor : IVideoFrameExtractor
{
    private readonly ILogger<VideoFrameExtractor> _logger;
    private readonly FrameExtractionOptions _frameOptions;
    private readonly string _workingDirectory;

    public VideoFrameExtractor(ILogger<VideoFrameExtractor> logger, IOptions<FrameExtractionOptions> frameOptions)
    {
        _logger = logger;
        _frameOptions = frameOptions.Value;
        
        // 解析並創建工作目錄
        _workingDirectory = ResolveOutputDirectory(_frameOptions.OutputDirectory);
        Directory.CreateDirectory(_workingDirectory);
        
        _logger.LogInformation("Frame extraction directory: {Directory}", _workingDirectory);
    }
    
    private string ResolveOutputDirectory(string configuredPath)
    {
        // 處理特殊路徑字符
        if (configuredPath.StartsWith("~/"))
        {
            var homePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            return Path.Combine(homePath, configuredPath.Substring(2));
        }
        
        // 如果是相對路徑，轉換為絕對路徑
        if (!Path.IsPathRooted(configuredPath))
        {
            return Path.GetFullPath(configuredPath);
        }
        
        return configuredPath;
    }
    
    private string GenerateFrameFileName(int index, TimeSpan timestamp)
    {
        var extension = _frameOptions.ImageFormat.ToLower();
        if (!extension.StartsWith("."))
        {
            extension = "." + extension;
        }
        
        if (_frameOptions.UseTimestampInFilename)
        {
            return $"frame_{index:D4}_{timestamp.TotalSeconds:F2}s{extension}";
        }
        else
        {
            return $"frame_{index:D4}{extension}";
        }
    }

    public async Task<List<ExtractedFrame>> ExtractFramesAsync(string videoPath, VideoAnalysisOptions options, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting frame extraction from: {VideoPath}", videoPath);
        
        if (!File.Exists(videoPath))
        {
            throw new FileNotFoundException($"Video file not found: {videoPath}");
        }

        var frames = new List<ExtractedFrame>();
        
        try
        {
            var videoDuration = await GetVideoDurationAsync(videoPath, cancellationToken);
            var extractionTimes = new List<TimeSpan>();

            if (options.UseSceneChangeDetection)
            {
                var sceneChanges = await DetectSceneChangesAsync(videoPath, cancellationToken);
                extractionTimes.AddRange(sceneChanges.Take(options.MaxFramesPerVideo));
                _logger.LogInformation("Found {Count} scene changes", sceneChanges.Count);
            }

            // If no scene changes or need more frames, add interval-based extractions
            if (extractionTimes.Count < options.MaxFramesPerVideo)
            {
                var intervalFrames = GenerateIntervalFrames(videoDuration, options.FrameExtractionIntervalSeconds, options.MaxFramesPerVideo - extractionTimes.Count);
                extractionTimes.AddRange(intervalFrames.Where(f => !extractionTimes.Any(e => Math.Abs((e - f).TotalSeconds) < 1)));
            }

            extractionTimes = extractionTimes.OrderBy(t => t).Take(options.MaxFramesPerVideo).ToList();

            // 為每個影片建立專用目錄（如果啟用）
            var extractionDirectory = _workingDirectory;
            if (_frameOptions.CreateSubDirectoryPerVideo)
            {
                var videoName = Path.GetFileNameWithoutExtension(videoPath);
                var timestamp = DateTime.Now;
                var subDirName = _frameOptions.SubDirectoryNameFormat
                    .Replace("{videoName}", videoName)
                    .Replace("{timestamp:yyyyMMdd_HHmmss}", timestamp.ToString("yyyyMMdd_HHmmss"));
                
                extractionDirectory = Path.Combine(_workingDirectory, subDirName);
                Directory.CreateDirectory(extractionDirectory);
                _logger.LogInformation("Created video-specific directory: {Directory}", extractionDirectory);
            }

            for (int i = 0; i < extractionTimes.Count; i++)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var timestamp = extractionTimes[i];
                var frameFileName = GenerateFrameFileName(i, timestamp);
                var framePath = Path.Combine(extractionDirectory, frameFileName);

                try
                {
                    await FFMpegArguments
                        .FromFileInput(videoPath)
                        .OutputToFile(framePath, true, options =>
                        {
                            options
                                .Seek(timestamp)
                                .WithFrameOutputCount(1)
                                .WithVideoCodec("mjpeg")
                                .WithCustomArgument("-q:v 2"); // High quality
                        })
                        .ProcessAsynchronously(true);

                    if (File.Exists(framePath))
                    {
                        var fileInfo = new FileInfo(framePath);
                        var imageInfo = await FFProbe.AnalyseAsync(framePath);
                        
                        frames.Add(new ExtractedFrame
                        {
                            Timestamp = timestamp,
                            FramePath = framePath,
                            Width = imageInfo.PrimaryVideoStream?.Width ?? 0,
                            Height = imageInfo.PrimaryVideoStream?.Height ?? 0,
                            FileSizeBytes = fileInfo.Length
                        });
                        
                        _logger.LogDebug("Extracted frame at {Timestamp} -> {FramePath}", timestamp, framePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to extract frame at timestamp {Timestamp}", timestamp);
                }
            }

            _logger.LogInformation("Successfully extracted {Count} frames from video", frames.Count);
            return frames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during frame extraction from: {VideoPath}", videoPath);
            throw;
        }
    }

    public async Task<TimeSpan> GetVideoDurationAsync(string videoPath, CancellationToken cancellationToken = default)
    {
        try
        {
            var mediaInfo = await FFProbe.AnalyseAsync(videoPath, cancellationToken: cancellationToken);
            return mediaInfo.Duration;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting video duration: {VideoPath}", videoPath);
            throw;
        }
    }

    public async Task<List<TimeSpan>> DetectSceneChangesAsync(string videoPath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Detecting scene changes in: {VideoPath}", videoPath);
        
        try
        {
            // Use FFmpeg scene detection filter
            var sceneDetectionOutput = Path.Combine(_workingDirectory, "scene_changes.txt");
            
            await FFMpegArguments
                .FromFileInput(videoPath)
                .OutputToFile("/dev/null", true, options =>
                {
                    options
                        .WithCustomArgument($"-vf \"select='gt(scene,0.3)',showinfo\"")
                        .WithCustomArgument("-f null");
                })
                .ProcessAsynchronously();

            // For now, return a simple interval-based approach as scene detection parsing is complex
            // In a full implementation, you would parse the showinfo output
            var duration = await GetVideoDurationAsync(videoPath, cancellationToken);
            return GenerateIntervalFrames(duration, 10, 50); // Every 10 seconds, max 50 points
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Scene change detection failed, falling back to interval extraction");
            var duration = await GetVideoDurationAsync(videoPath, cancellationToken);
            return GenerateIntervalFrames(duration, 10, 20);
        }
    }

    private List<TimeSpan> GenerateIntervalFrames(TimeSpan duration, int intervalSeconds, int maxFrames)
    {
        var frames = new List<TimeSpan>();
        var totalSeconds = (int)duration.TotalSeconds;
        var actualInterval = Math.Max(intervalSeconds, totalSeconds / maxFrames);
        
        for (int second = actualInterval; second < totalSeconds; second += actualInterval)
        {
            frames.Add(TimeSpan.FromSeconds(second));
            if (frames.Count >= maxFrames) break;
        }
        
        return frames;
    }

    public void Dispose()
    {
        try
        {
            // 只有在設定不保留框架時才清理
            if (!_frameOptions.KeepFrames && Directory.Exists(_workingDirectory))
            {
                Directory.Delete(_workingDirectory, true);
                _logger.LogDebug("Cleaned up frame directory: {WorkingDirectory}", _workingDirectory);
            }
            else if (_frameOptions.KeepFrames)
            {
                _logger.LogInformation("Frame files preserved at: {WorkingDirectory}", _workingDirectory);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to clean up frame directory: {WorkingDirectory}", _workingDirectory);
        }
    }
}