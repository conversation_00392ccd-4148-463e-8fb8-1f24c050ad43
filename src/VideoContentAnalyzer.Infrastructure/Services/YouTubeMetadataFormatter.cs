using System.Text;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.Services;

/// <summary>
/// YouTube 元數據格式化服務實現
/// 將 YouTubeVideoInfo 格式化為適合 AI 理解的文字描述
/// </summary>
public class YouTubeMetadataFormatter : IYouTubeMetadataFormatter
{
    public string FormatMetadataForAI(YouTubeVideoInfo? videoInfo)
    {
        if (videoInfo == null)
            return string.Empty;

        var sb = new StringBuilder();
        sb.AppendLine("=== YouTube 影片背景資訊 ===");
        sb.AppendLine("以下是這部影片的詳細背景資訊，請在分析截圖時參考這些上下文來提供更準確的識別和分析：");
        sb.AppendLine();

        // 影片背景資訊
        var backgroundInfo = FormatVideoBackground(videoInfo);
        if (!string.IsNullOrEmpty(backgroundInfo))
        {
            sb.AppendLine(backgroundInfo);
            sb.AppendLine();
        }

        // 頻道資訊
        var channelInfo = FormatChannelInfo(videoInfo);
        if (!string.IsNullOrEmpty(channelInfo))
        {
            sb.AppendLine(channelInfo);
            sb.AppendLine();
        }

        // 地理位置資訊
        var locationInfo = FormatLocationInfo(videoInfo);
        if (!string.IsNullOrEmpty(locationInfo))
        {
            sb.AppendLine(locationInfo);
            sb.AppendLine();
        }

        // 標籤和關鍵字
        var tagsInfo = FormatTagsAndKeywords(videoInfo);
        if (!string.IsNullOrEmpty(tagsInfo))
        {
            sb.AppendLine(tagsInfo);
            sb.AppendLine();
        }

        // 統計資訊
        var statsInfo = FormatStatistics(videoInfo);
        if (!string.IsNullOrEmpty(statsInfo))
        {
            sb.AppendLine(statsInfo);
            sb.AppendLine();
        }

        sb.AppendLine("請根據以上背景資訊來增強您對截圖內容的理解和分析準確性。");
        sb.AppendLine("=== 背景資訊結束 ===");
        sb.AppendLine();

        return sb.ToString();
    }

    public string FormatVideoBackground(YouTubeVideoInfo videoInfo)
    {
        var sb = new StringBuilder();
        sb.AppendLine("📹 影片背景：");
        sb.AppendLine($"• 標題：{videoInfo.Title}");
        
        if (!string.IsNullOrEmpty(videoInfo.Description))
        {
            // 限制描述長度，避免 prompt 過長
            var description = videoInfo.Description.Length > 500 
                ? videoInfo.Description[..500] + "..." 
                : videoInfo.Description;
            sb.AppendLine($"• 描述：{description}");
        }

        if (!string.IsNullOrEmpty(videoInfo.CategoryName))
        {
            sb.AppendLine($"• 分類：{videoInfo.CategoryName}");
        }

        if (!string.IsNullOrEmpty(videoInfo.DefaultLanguage))
        {
            sb.AppendLine($"• 語言：{videoInfo.DefaultLanguage}");
        }

        if (videoInfo.UploadDate.HasValue)
        {
            sb.AppendLine($"• 上傳時間：{videoInfo.UploadDate.Value:yyyy-MM-dd}");
        }

        return sb.ToString();
    }

    public string FormatChannelInfo(YouTubeVideoInfo videoInfo)
    {
        var sb = new StringBuilder();
        
        if (!string.IsNullOrEmpty(videoInfo.Channel) || videoInfo.ChannelInfo != null)
        {
            sb.AppendLine("📺 頻道資訊：");
            
            if (!string.IsNullOrEmpty(videoInfo.Channel))
            {
                sb.AppendLine($"• 頻道名稱：{videoInfo.Channel}");
            }

            if (videoInfo.ChannelInfo != null)
            {
                if (videoInfo.ChannelInfo.SubscriberCount.HasValue)
                {
                    sb.AppendLine($"• 訂閱者數：{FormatNumber(videoInfo.ChannelInfo.SubscriberCount.Value)}");
                }

                if (!string.IsNullOrEmpty(videoInfo.ChannelInfo.Country))
                {
                    sb.AppendLine($"• 頻道國家：{videoInfo.ChannelInfo.Country}");
                }

                if (videoInfo.ChannelInfo.Keywords.Any())
                {
                    sb.AppendLine($"• 頻道關鍵字：{string.Join(", ", videoInfo.ChannelInfo.Keywords.Take(10))}");
                }
            }
        }

        return sb.ToString();
    }

    public string FormatLocationInfo(YouTubeVideoInfo videoInfo)
    {
        var sb = new StringBuilder();
        
        if (videoInfo.DetectedPlaces.Any())
        {
            sb.AppendLine("📍 檢測到的地點資訊：");
            
            foreach (var place in videoInfo.DetectedPlaces.Take(5)) // 限制顯示數量
            {
                sb.AppendLine($"• {place.Name}");
                
                if (!string.IsNullOrEmpty(place.FormattedAddress))
                {
                    sb.AppendLine($"  地址：{place.FormattedAddress}");
                }
                
                if (place.Types.Any())
                {
                    sb.AppendLine($"  類型：{string.Join(", ", place.Types)}");
                }
                
                if (!string.IsNullOrEmpty(place.DetectionSource))
                {
                    sb.AppendLine($"  來源：{place.DetectionSource}");
                }
            }
        }

        return sb.ToString();
    }

    public string FormatTagsAndKeywords(YouTubeVideoInfo videoInfo)
    {
        var sb = new StringBuilder();
        
        if (videoInfo.Tags.Any() || videoInfo.ExtractedKeywords.Any())
        {
            sb.AppendLine("🏷️ 主題標籤和關鍵字：");
            
            if (videoInfo.Tags.Any())
            {
                sb.AppendLine($"• 影片標籤：{string.Join(", ", videoInfo.Tags.Take(15))}");
            }
            
            if (videoInfo.ExtractedKeywords.Any())
            {
                sb.AppendLine($"• 萃取關鍵字：{string.Join(", ", videoInfo.ExtractedKeywords.Take(15))}");
            }
        }

        return sb.ToString();
    }

    public string FormatStatistics(YouTubeVideoInfo videoInfo)
    {
        var sb = new StringBuilder();
        
        if (videoInfo.ViewCount.HasValue || videoInfo.LikeCount.HasValue || videoInfo.CommentCount.HasValue)
        {
            sb.AppendLine("📊 影片統計：");
            
            if (videoInfo.ViewCount.HasValue)
            {
                sb.AppendLine($"• 觀看次數：{FormatNumber(videoInfo.ViewCount.Value)}");
            }
            
            if (videoInfo.LikeCount.HasValue)
            {
                sb.AppendLine($"• 按讚數：{FormatNumber(videoInfo.LikeCount.Value)}");
            }
            
            if (videoInfo.CommentCount.HasValue)
            {
                sb.AppendLine($"• 留言數：{FormatNumber(videoInfo.CommentCount.Value)}");
            }

            // 計算參與度
            if (videoInfo.Statistics != null)
            {
                var engagementRate = videoInfo.Statistics.EngagementRate;
                if (engagementRate > 0)
                {
                    sb.AppendLine($"• 參與度：{engagementRate:P2}");
                }
            }
        }

        return sb.ToString();
    }

    /// <summary>
    /// 格式化數字為易讀格式
    /// </summary>
    private static string FormatNumber(long number)
    {
        if (number >= 1_000_000_000)
            return $"{number / 1_000_000_000.0:F1}B";
        if (number >= 1_000_000)
            return $"{number / 1_000_000.0:F1}M";
        if (number >= 1_000)
            return $"{number / 1_000.0:F1}K";
        return number.ToString();
    }
}
