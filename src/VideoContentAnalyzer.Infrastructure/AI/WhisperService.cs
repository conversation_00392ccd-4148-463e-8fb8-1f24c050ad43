using FFMpegCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Globalization;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;
using Whisper.net;

namespace VideoContentAnalyzer.Infrastructure.AI;

public class WhisperService : IWhisperService
{
    private readonly ILogger<WhisperService> _logger;
    private readonly WhisperOptions _options;
    private readonly string _tempDirectory;
    private WhisperFactory? _whisperFactory;
    private WhisperProcessor? _processor;

    public WhisperService(IOptions<WhisperOptions> options, ILogger<WhisperService> logger)
    {
        _options = options.Value;
        _logger = logger;
        
        // 使用專案內的統一音頻暫存目錄
        _tempDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "..", "..", "..", "..", "data", "audio", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_tempDirectory);
    }

    public async Task<List<SubtitleSegment>> TranscribeAudioAsync(string audioPath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting audio transcription: {AudioPath}", audioPath);
        
        if (!File.Exists(audioPath))
        {
            throw new FileNotFoundException($"Audio file not found: {audioPath}");
        }

        try
        {
            // First try using Whisper.net
            return await TranscribeUsingDotNetAsync(audioPath, cancellationToken);
        }
        catch (Exception ex) when (ex.Message.Contains("Native Library not found"))
        {
            _logger.LogWarning("Whisper.net native library not found, falling back to CLI: {Error}", ex.Message);
            return await TranscribeUsingCliAsync(audioPath, cancellationToken);
        }
        catch (FileNotFoundException ex) when (ex.Message.Contains("Native Library not found"))
        {
            _logger.LogWarning("Whisper.net initialization failed, falling back to CLI: {Error}", ex.Message);
            return await TranscribeUsingCliAsync(audioPath, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during audio transcription with Whisper.net, trying CLI fallback: {AudioPath}", audioPath);
            try
            {
                return await TranscribeUsingCliAsync(audioPath, cancellationToken);
            }
            catch (Exception cliEx)
            {
                _logger.LogError(cliEx, "Both Whisper.net and CLI transcription failed");
                throw new InvalidOperationException($"All transcription methods failed. Whisper.net: {ex.Message}, CLI: {cliEx.Message}", ex);
            }
        }
    }

    private async Task<List<SubtitleSegment>> TranscribeUsingDotNetAsync(string audioPath, CancellationToken cancellationToken)
    {
        await EnsureWhisperInitializedAsync(cancellationToken);
        
        var segments = new List<SubtitleSegment>();
        
        using var fileStream = File.OpenRead(audioPath);
        
        await foreach (var result in _processor!.ProcessAsync(fileStream, cancellationToken))
        {
            var segment = new SubtitleSegment
            {
                StartTime = result.Start,
                EndTime = result.End,
                Text = result.Text.Trim(),
                Language = result.Language ?? "unknown",
                Confidence = (double)result.Probability
            };
            
            segments.Add(segment);
            
            _logger.LogDebug("Transcribed segment [{Start}-{End}]: {Text}", 
                segment.StartTime, segment.EndTime, segment.Text);
        }
        
        _logger.LogInformation("Whisper.net transcription completed. Generated {Count} segments", segments.Count);
        return segments;
    }

    private async Task<List<SubtitleSegment>> TranscribeUsingCliAsync(string audioPath, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting CLI transcription for: {AudioPath}", audioPath);
        
        // Check if Whisper CLI is available
        if (!await IsWhisperCliAvailableAsync())
        {
            throw new InvalidOperationException("Whisper CLI is not available. Please install it using: pip install openai-whisper");
        }

        var outputDir = Path.Combine(_tempDirectory, "whisper_output");
        Directory.CreateDirectory(outputDir);
        
        var language = _options.Language == "auto" ? "" : $" --language {_options.Language}";
        var modelSize = _options.ModelSize ?? "base";
        
        var arguments = $"\"{audioPath}\" --model {modelSize} --output_format srt --output_dir \"{outputDir}\"{language}";
        
        _logger.LogDebug("Running Whisper CLI: whisper {Arguments}", arguments);
        
        var process = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = "whisper",
                Arguments = arguments,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true
            }
        };

        var output = new List<string>();
        var errors = new List<string>();
        
        process.OutputDataReceived += (s, e) => { if (e.Data != null) output.Add(e.Data); };
        process.ErrorDataReceived += (s, e) => { if (e.Data != null) errors.Add(e.Data); };
        
        process.Start();
        process.BeginOutputReadLine();
        process.BeginErrorReadLine();
        
        await process.WaitForExitAsync(cancellationToken);
        
        if (process.ExitCode != 0)
        {
            var errorMessage = string.Join(Environment.NewLine, errors);
            throw new InvalidOperationException($"Whisper CLI failed with exit code {process.ExitCode}: {errorMessage}");
        }
        
        // Find the generated SRT file
        var audioFileName = Path.GetFileNameWithoutExtension(audioPath);
        var srtFile = Path.Combine(outputDir, $"{audioFileName}.srt");
        
        if (!File.Exists(srtFile))
        {
            throw new FileNotFoundException($"Expected SRT file not found: {srtFile}");
        }
        
        // Parse the SRT file
        var segments = await ParseSrtFileAsync(srtFile, cancellationToken);
        
        _logger.LogInformation("CLI transcription completed. Generated {Count} segments from SRT file", segments.Count);
        
        // Clean up temporary SRT file
        try
        {
            File.Delete(srtFile);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete temporary SRT file: {SrtFile}", srtFile);
        }
        
        return segments;
    }

    private async Task<bool> IsWhisperCliAvailableAsync()
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "whisper",
                    Arguments = "--help",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                }
            };
            
            process.Start();
            await process.WaitForExitAsync();
            
            return process.ExitCode == 0;
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Whisper CLI availability check failed");
            return false;
        }
    }

    private async Task<List<SubtitleSegment>> ParseSrtFileAsync(string srtPath, CancellationToken cancellationToken)
    {
        var segments = new List<SubtitleSegment>();
        var lines = await File.ReadAllLinesAsync(srtPath, cancellationToken);
        
        SubtitleSegment? currentSegment = null;
        var textLines = new List<string>();
        
        for (int i = 0; i < lines.Length; i++)
        {
            var line = lines[i].Trim();
            
            // Skip empty lines
            if (string.IsNullOrEmpty(line))
            {
                if (currentSegment != null)
                {
                    currentSegment.Text = string.Join(" ", textLines).Trim();
                    segments.Add(currentSegment);
                    currentSegment = null;
                    textLines.Clear();
                }
                continue;
            }
            
            // Check if it's a sequence number
            if (int.TryParse(line, out _))
            {
                currentSegment = new SubtitleSegment { Text = string.Empty };
                continue;
            }
            
            // Check if it's a timestamp line
            if (line.Contains("-->") && currentSegment != null)
            {
                var parts = line.Split(new[] { " --> " }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 2)
                {
                    if (TryParseTimeSpan(parts[0], out var startTime))
                        currentSegment.StartTime = startTime;
                    if (TryParseTimeSpan(parts[1], out var endTime))
                        currentSegment.EndTime = endTime;
                }
                continue;
            }
            
            // It's a text line
            if (currentSegment != null)
            {
                textLines.Add(line);
            }
        }
        
        // Add the last segment if exists
        if (currentSegment != null && textLines.Count > 0)
        {
            currentSegment.Text = string.Join(" ", textLines).Trim();
            segments.Add(currentSegment);
        }
        
        // Set default values for CLI-generated segments
        foreach (var segment in segments)
        {
            segment.Language = _options.Language == "auto" ? "unknown" : _options.Language;
            segment.Confidence = 0.8; // Default confidence for CLI transcription
        }
        
        return segments;
    }
    
    private static bool TryParseTimeSpan(string timeString, out TimeSpan timeSpan)
    {
        timeSpan = TimeSpan.Zero;
        
        try
        {
            // SRT format: 00:00:20,000
            var parts = timeString.Split(':');
            if (parts.Length != 3) return false;
            
            var hours = int.Parse(parts[0]);
            var minutes = int.Parse(parts[1]);
            
            var secondsParts = parts[2].Split(',');
            if (secondsParts.Length != 2) return false;
            
            var seconds = int.Parse(secondsParts[0]);
            var milliseconds = int.Parse(secondsParts[1]);
            
            timeSpan = new TimeSpan(0, hours, minutes, seconds, milliseconds);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<string> ExtractAudioFromVideoAsync(string videoPath, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Extracting audio from video: {VideoPath}", videoPath);
        
        if (!File.Exists(videoPath))
        {
            throw new FileNotFoundException($"Video file not found: {videoPath}");
        }

        try
        {
            var audioFileName = $"{Path.GetFileNameWithoutExtension(videoPath)}_audio.wav";
            var audioPath = Path.Combine(_tempDirectory, audioFileName);

            await FFMpegArguments
                .FromFileInput(videoPath)
                .OutputToFile(audioPath, true, options =>
                {
                    options
                        .WithAudioCodec("pcm_s16le")
                        .WithAudioSamplingRate(16000)
                        .WithCustomArgument("-ac 1"); // mono
                })
                .ProcessAsynchronously(true);

            if (!File.Exists(audioPath))
            {
                throw new InvalidOperationException("Failed to extract audio from video");
            }

            _logger.LogInformation("Successfully extracted audio to: {AudioPath}", audioPath);
            return audioPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting audio from video: {VideoPath}", videoPath);
            throw;
        }
    }

    public async Task<bool> IsWhisperAvailableAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await EnsureWhisperInitializedAsync(cancellationToken);
            return _processor != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Whisper.net is not available, checking CLI fallback");
            return await IsWhisperCliAvailableAsync();
        }
    }

    private async Task EnsureWhisperInitializedAsync(CancellationToken cancellationToken)
    {
        if (_processor != null) return;

        _logger.LogInformation("Initializing Whisper with model: {ModelPath}", _options.ModelPath);

        try
        {
            // Ensure model file exists
            if (!File.Exists(_options.ModelPath))
            {
                _logger.LogInformation("Whisper model not found at {ModelPath}, attempting to download...", _options.ModelPath);
                await DownloadWhisperModelAsync(cancellationToken);
            }

            _whisperFactory = WhisperFactory.FromPath(_options.ModelPath);
            
            var processorBuilder = _whisperFactory.CreateBuilder()
                .WithLanguage(_options.Language)
                .WithThreads(_options.Threads)
                .WithProbabilities();

            if (_options.Translate)
            {
                processorBuilder = processorBuilder.WithTranslate();
            }

            _processor = processorBuilder.Build();
            
            _logger.LogInformation("Whisper initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Whisper");
            throw;
        }
    }

    private Task DownloadWhisperModelAsync(CancellationToken cancellationToken)
    {
        // In a real implementation, you would download the model from OpenAI
        // For now, we'll provide instructions for manual download
        var modelDir = Path.GetDirectoryName(_options.ModelPath)!;
        Directory.CreateDirectory(modelDir);
        
        return Task.CompletedTask;

        var message = $"""
            Whisper model not found at: {_options.ModelPath}
            
            Please download the Whisper model manually:
            1. Visit: https://huggingface.co/ggerganov/whisper.cpp
            2. Download the {_options.ModelSize} model (ggml-{_options.ModelSize}.bin)
            3. Place it at: {_options.ModelPath}
            
            Or use whisper.cpp to download:
            bash download-ggml-model.sh {_options.ModelSize}
            """;

        _logger.LogError(message);
        throw new FileNotFoundException(message);
    }

    public void Dispose()
    {
        try
        {
            _processor?.Dispose();
            _whisperFactory?.Dispose();
            
            if (Directory.Exists(_tempDirectory))
            {
                Directory.Delete(_tempDirectory, true);
                _logger.LogDebug("Cleaned up temporary audio directory: {TempDirectory}", _tempDirectory);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during WhisperService cleanup");
        }
    }
}

public class WhisperOptions
{
    public string ModelPath { get; set; } = Path.Combine(
        Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), 
        ".whisper", "ggml-base.bin");
    public string ModelSize { get; set; } = "base";
    public string Language { get; set; } = "auto";
    public bool Translate { get; set; } = false;
    public int Threads { get; set; } = Environment.ProcessorCount;
}