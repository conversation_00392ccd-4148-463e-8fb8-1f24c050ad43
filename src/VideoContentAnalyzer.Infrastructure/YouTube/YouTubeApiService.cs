using Google.Apis.Services;
using Google.Apis.YouTube.v3;
using Google.Apis.YouTube.v3.Data;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Xml;
using VideoContentAnalyzer.Core.Interfaces;
using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Infrastructure.YouTube;

public class YouTubeApiService : IYouTubeApiService
{
    private readonly YouTubeService _youTubeService;
    private readonly YouTubeApiOptions _options;
    private readonly ILogger<YouTubeApiService> _logger;
    private readonly Dictionary<string, Core.Models.VideoCategory> _categoryCache = new();

    public YouTubeApiService(IOptions<YouTubeApiOptions> options, ILogger<YouTubeApiService> logger)
    {
        _options = options.Value;
        _logger = logger;
        
        // 詳細記錄配置載入情況
        _logger.LogDebug("YouTube API Service 初始化:");
        _logger.LogDebug("  ApiKey 來源: {Source}", _options.ApiKey == "YOUR_YOUTUBE_API_KEY_HERE" ? "YAML 預設值" : "環境變數或覆寫值");
        _logger.LogDebug("  ApiKey 值: {ApiKey}", _options.ApiKey);
        _logger.LogDebug("  ApplicationName: {AppName}", _options.ApplicationName);
        _logger.LogDebug("  DefaultRegionCode: {RegionCode}", _options.DefaultRegionCode);
        
        // 檢查環境變數
        var envApiKey = Environment.GetEnvironmentVariable("YOUTUBE_API_KEY");
        _logger.LogDebug("  環境變數 YOUTUBE_API_KEY: {EnvKey}", string.IsNullOrEmpty(envApiKey) ? "未設定" : envApiKey);
        
        _youTubeService = new YouTubeService(new BaseClientService.Initializer()
        {
            ApiKey = _options.ApiKey,
            ApplicationName = _options.ApplicationName
        });
    }

    public async Task<YouTubeVideoInfo?> GetVideoDetailsAsync(string videoId)
    {
        try
        {
            _logger.LogDebug("正在透過 YouTube API 取得影片資訊: {VideoId}", videoId);

            var videoRequest = _youTubeService.Videos.List("snippet,statistics,contentDetails");
            videoRequest.Id = videoId;

            var videoResponse = await videoRequest.ExecuteAsync();
            
            if (videoResponse.Items?.Count == 0)
            {
                _logger.LogWarning("YouTube API 未找到影片: {VideoId}", videoId);
                return null;
            }

            var videoItem = videoResponse.Items![0];
            
            var videoInfo = new YouTubeVideoInfo
            {
                Id = videoItem.Id,
                Title = videoItem.Snippet.Title ?? "",
                Description = videoItem.Snippet.Description,
                Url = $"https://www.youtube.com/watch?v={videoId}",
                Channel = videoItem.Snippet.ChannelTitle,
                ChannelId = videoItem.Snippet.ChannelId,
                UploadDate = videoItem.Snippet.PublishedAtDateTimeOffset?.ToLocalTime().DateTime,
                ThumbnailUrl = videoItem.Snippet.Thumbnails?.High?.Url ?? 
                             videoItem.Snippet.Thumbnails?.Medium?.Url ??
                             videoItem.Snippet.Thumbnails?.Default__.Url,
                ViewCount = (long?)videoItem.Statistics?.ViewCount,
                LikeCount = (long?)videoItem.Statistics?.LikeCount,
                CommentCount = (long?)videoItem.Statistics?.CommentCount,
                Duration = ParseDuration(videoItem.ContentDetails?.Duration ?? ""),
                CategoryId = videoItem.Snippet.CategoryId,
                CategoryName = await GetCategoryNameAsync(videoItem.Snippet.CategoryId ?? ""),
                DefaultLanguage = videoItem.Snippet.DefaultLanguage,
                DefaultAudioLanguage = videoItem.Snippet.DefaultAudioLanguage
            };

            _logger.LogInformation("成功取得 YouTube 影片資訊: {Title} (觀看次數: {ViewCount}, 分類: {Category})", 
                videoInfo.Title, videoInfo.ViewCount, videoInfo.CategoryName);
            
            _logger.LogDebug("影片語言資訊: DefaultLanguage={DefaultLanguage}, DefaultAudioLanguage={DefaultAudioLanguage}", 
                videoInfo.DefaultLanguage, videoInfo.DefaultAudioLanguage);

            return videoInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取得 YouTube 影片資訊時發生錯誤: {VideoId}", videoId);
            return null;
        }
    }

    public async Task<string> GetCategoryNameAsync(string categoryId)
    {
        if (string.IsNullOrEmpty(categoryId))
            return "未知分類";

        try
        {
            // 檢查快取
            if (_categoryCache.TryGetValue(categoryId, out var cachedCategory))
            {
                return cachedCategory.Title;
            }

            // 載入所有分類（如果快取為空）
            if (_categoryCache.Count == 0)
            {
                await LoadVideoCategoriesAsync();
            }

            return _categoryCache.TryGetValue(categoryId, out var category) 
                ? category.Title 
                : "未知分類";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "取得影片分類名稱時發生錯誤: {CategoryId}", categoryId);
            return "未知分類";
        }
    }

    public async Task<Dictionary<string, Core.Models.VideoCategory>> LoadVideoCategoriesAsync()
    {
        try
        {
            if (_categoryCache.Count > 0)
            {
                return _categoryCache;
            }

            _logger.LogDebug("正在載入 YouTube 影片分類: {RegionCode}", _options.DefaultRegionCode);

            var categoriesRequest = _youTubeService.VideoCategories.List("snippet");
            categoriesRequest.RegionCode = _options.DefaultRegionCode;

            var categoriesResponse = await categoriesRequest.ExecuteAsync();

            foreach (var category in categoriesResponse.Items ?? new List<Google.Apis.YouTube.v3.Data.VideoCategory>())
            {
                _categoryCache[category.Id] = new Core.Models.VideoCategory
                {
                    Id = category.Id,
                    Title = category.Snippet?.Title ?? "未知",
                    Assignable = category.Snippet?.Assignable ?? false
                };
            }

            _logger.LogInformation("成功載入 {Count} 個 YouTube 影片分類", _categoryCache.Count);
            
            return _categoryCache;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "載入 YouTube 影片分類時發生錯誤");
            return _categoryCache;
        }
    }

    public string MapVideoLanguageToSubtitle(string? videoLanguage)
    {
        if (string.IsNullOrEmpty(videoLanguage))
            return _options.PreferredSubtitleLanguages.FirstOrDefault() ?? "zh-TW";

        return videoLanguage.ToLower() switch
        {
            "zh-tw" or "zh-hant" or "zh-hant-tw" => "zh-TW",
            "zh-cn" or "zh-hans" or "zh-hans-cn" => "zh-CN", 
            "en" or "en-us" or "en-gb" => "en",
            "ja" or "ja-jp" => "ja",
            "ko" or "ko-kr" => "ko",
            "es" or "es-es" => "es",
            "fr" or "fr-fr" => "fr",
            "de" or "de-de" => "de",
            "it" or "it-it" => "it",
            "pt" or "pt-br" or "pt-pt" => "pt",
            "ru" or "ru-ru" => "ru",
            "ar" or "ar-sa" => "ar",
            "hi" or "hi-in" => "hi",
            "th" or "th-th" => "th",
            "vi" or "vi-vn" => "vi",
            _ => videoLanguage.Split('-')[0] // 取語言代碼的主要部分
        };
    }

    public string GetPreferredSubtitleLanguage(YouTubeVideoInfo videoInfo)
    {
        // 優先使用影片音訊語言
        if (!string.IsNullOrEmpty(videoInfo.DefaultAudioLanguage))
        {
            var mappedLanguage = MapVideoLanguageToSubtitle(videoInfo.DefaultAudioLanguage);
            _logger.LogDebug("偵測到影片音訊語言: {AudioLanguage} → {MappedLanguage}", 
                videoInfo.DefaultAudioLanguage, mappedLanguage);
            return mappedLanguage;
        }
        
        // 其次使用影片元資料語言
        if (!string.IsNullOrEmpty(videoInfo.DefaultLanguage))
        {
            var mappedLanguage = MapVideoLanguageToSubtitle(videoInfo.DefaultLanguage);
            _logger.LogDebug("偵測到影片元資料語言: {DefaultLanguage} → {MappedLanguage}", 
                videoInfo.DefaultLanguage, mappedLanguage);
            return mappedLanguage;
        }
        
        // 使用預設偏好語言
        var defaultLanguage = _options.PreferredSubtitleLanguages.FirstOrDefault() ?? "zh-TW";
        _logger.LogDebug("未偵測到影片語言資訊，使用預設語言: {DefaultLanguage}", defaultLanguage);
        return defaultLanguage;
    }

    public bool IsAvailable()
    {
        var hasValidKey = !string.IsNullOrEmpty(_options.ApiKey) && 
                         _options.ApiKey != "YOUR_YOUTUBE_API_KEY_HERE";
        
        _logger.LogDebug("YouTube API 可用性檢查: {IsAvailable}, API Key: '{ApiKey}' (length: {Length})", 
            hasValidKey, 
            string.IsNullOrEmpty(_options.ApiKey) ? "空值" : _options.ApiKey.Substring(0, Math.Min(10, _options.ApiKey.Length)) + "...", 
            _options.ApiKey?.Length ?? 0);
        
        return hasValidKey;
    }

    private static TimeSpan ParseDuration(string duration)
    {
        try
        {
            if (string.IsNullOrEmpty(duration))
                return TimeSpan.Zero;
                
            return XmlConvert.ToTimeSpan(duration);
        }
        catch (Exception)
        {
            return TimeSpan.Zero;
        }
    }

    // 新增的元資料收集方法實現
    public async Task<YouTubeMetadataCollectionResult> CollectVideoMetadataAsync(string videoId, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var result = new YouTubeMetadataCollectionResult
        {
            Success = false,
            VideoInfo = new YouTubeVideoInfo { Id = videoId, Title = "", Url = $"https://www.youtube.com/watch?v={videoId}" }
        };

        try
        {
            _logger.LogInformation("開始收集 YouTube 影片完整元資料: {VideoId}", videoId);

            // 1. 取得基本影片資訊
            var videoInfo = await GetVideoDetailsAsync(videoId);
            if (videoInfo == null)
            {
                result.ErrorMessage = "無法取得影片基本資訊";
                return result;
            }

            result.VideoInfo = videoInfo;

            // 2. 並行收集其他元資料
            var tasks = new List<Task>
            {
                // 收集頻道資訊
                Task.Run(async () =>
                {
                    if (!string.IsNullOrEmpty(videoInfo.ChannelId))
                    {
                        result.ChannelInfo = await GetChannelDetailsAsync(videoInfo.ChannelId, cancellationToken);
                        videoInfo.ChannelInfo = result.ChannelInfo;
                    }
                }, cancellationToken),

                // 收集影片留言
                Task.Run(async () =>
                {
                    result.Comments = await GetVideoCommentsAsync(videoId, 20, cancellationToken);
                    videoInfo.TopComments = result.Comments;
                }, cancellationToken),

                // 收集字幕資訊
                Task.Run(async () =>
                {
                    result.AvailableSubtitles = await GetAvailableSubtitlesAsync(videoId, cancellationToken);
                    videoInfo.AvailableSubtitles = result.AvailableSubtitles;
                }, cancellationToken),

                // 關鍵字萃取
                Task.Run(async () =>
                {
                    result.ExtractedKeywords = await ExtractKeywordsFromVideoAsync(videoInfo, cancellationToken);
                    videoInfo.ExtractedKeywords = result.ExtractedKeywords;
                }, cancellationToken)
            };

            await Task.WhenAll(tasks);

            // 3. 更新統計資訊
            videoInfo.Statistics = new YouTubeVideoStatistics
            {
                ViewCount = videoInfo.ViewCount,
                LikeCount = videoInfo.LikeCount,
                DislikeCount = videoInfo.DislikeCount,
                CommentCount = videoInfo.CommentCount,
                LastUpdated = DateTime.UtcNow
            };

            result.Success = true;
            result.CollectionDuration = DateTime.UtcNow - startTime;

            _logger.LogInformation("成功收集 YouTube 影片元資料: {VideoId}, 耗時: {Duration}ms", 
                videoId, result.CollectionDuration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "收集 YouTube 影片元資料時發生錯誤: {VideoId}", videoId);
            result.ErrorMessage = ex.Message;
            result.CollectionDuration = DateTime.UtcNow - startTime;
            return result;
        }
    }

    public async Task<YouTubeChannelInfo?> GetChannelDetailsAsync(string channelId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("正在取得頻道詳細資訊: {ChannelId}", channelId);

            var channelRequest = _youTubeService.Channels.List("snippet,statistics,brandingSettings");
            channelRequest.Id = channelId;

            var channelResponse = await channelRequest.ExecuteAsync();

            if (channelResponse.Items?.Count == 0)
            {
                _logger.LogWarning("找不到頻道: {ChannelId}", channelId);
                return null;
            }

            var channelItem = channelResponse.Items![0];

            var channelInfo = new YouTubeChannelInfo
            {
                Id = channelItem.Id,
                Title = channelItem.Snippet?.Title ?? "",
                Description = channelItem.Snippet?.Description,
                SubscriberCount = (long?)channelItem.Statistics?.SubscriberCount,
                VideoCount = (long?)channelItem.Statistics?.VideoCount,
                ViewCount = (long?)channelItem.Statistics?.ViewCount,
                CreatedAt = channelItem.Snippet?.PublishedAtDateTimeOffset?.ToLocalTime().DateTime,
                Country = channelItem.Snippet?.Country,
                ThumbnailUrl = channelItem.Snippet?.Thumbnails?.High?.Url,
                BannerUrl = channelItem.BrandingSettings?.Image?.BannerExternalUrl
            };

            // 提取頻道關鍵字
            if (channelItem.BrandingSettings?.Channel?.Keywords != null)
            {
                channelInfo.Keywords = channelItem.BrandingSettings.Channel.Keywords
                    .Split(' ', StringSplitOptions.RemoveEmptyEntries)
                    .Where(k => !string.IsNullOrWhiteSpace(k))
                    .ToList();
            }

            _logger.LogInformation("成功取得頻道資訊: {ChannelTitle} (訂閱者: {SubscriberCount})", 
                channelInfo.Title, channelInfo.SubscriberCount);

            return channelInfo;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "取得頻道詳細資訊時發生錯誤: {ChannelId}", channelId);
            return null;
        }
    }

    public async Task<List<YouTubeComment>> GetVideoCommentsAsync(string videoId, int maxResults = 20, CancellationToken cancellationToken = default)
    {
        var comments = new List<YouTubeComment>();

        try
        {
            _logger.LogDebug("正在取得影片留言: {VideoId}, 數量: {MaxResults}", videoId, maxResults);

            var commentRequest = _youTubeService.CommentThreads.List("snippet,replies");
            commentRequest.VideoId = videoId;
            commentRequest.MaxResults = Math.Min(maxResults, 100);
            commentRequest.Order = CommentThreadsResource.ListRequest.OrderEnum.Relevance;

            var commentResponse = await commentRequest.ExecuteAsync();

            foreach (var item in commentResponse.Items ?? new List<Google.Apis.YouTube.v3.Data.CommentThread>())
            {
                var topComment = item.Snippet?.TopLevelComment?.Snippet;
                if (topComment != null)
                {
                    comments.Add(new YouTubeComment
                    {
                        Id = item.Snippet.TopLevelComment.Id,
                        Text = topComment.TextDisplay ?? "",
                        AuthorDisplayName = topComment.AuthorDisplayName ?? "",
                        AuthorProfileImageUrl = topComment.AuthorProfileImageUrl,
                        AuthorChannelId = topComment.AuthorChannelId?.Value,
                        LikeCount = topComment.LikeCount ?? 0,
                        PublishedAt = topComment.PublishedAtDateTimeOffset?.ToLocalTime().DateTime ?? DateTime.MinValue,
                        UpdatedAt = topComment.UpdatedAtDateTimeOffset?.ToLocalTime().DateTime
                    });
                }

                // 加入回覆
                if (item.Replies?.Comments != null)
                {
                    foreach (var reply in item.Replies.Comments.Take(3)) // 限制每個主留言的回覆數
                    {
                        var replySnippet = reply.Snippet;
                        if (replySnippet != null)
                        {
                            comments.Add(new YouTubeComment
                            {
                                Id = reply.Id,
                                Text = replySnippet.TextDisplay ?? "",
                                AuthorDisplayName = replySnippet.AuthorDisplayName ?? "",
                                AuthorProfileImageUrl = replySnippet.AuthorProfileImageUrl,
                                AuthorChannelId = replySnippet.AuthorChannelId?.Value,
                                LikeCount = replySnippet.LikeCount ?? 0,
                                PublishedAt = replySnippet.PublishedAtDateTimeOffset?.ToLocalTime().DateTime ?? DateTime.MinValue,
                                UpdatedAt = replySnippet.UpdatedAtDateTimeOffset?.ToLocalTime().DateTime,
                                ParentId = item.Snippet.TopLevelComment.Id
                            });
                        }
                    }
                }
            }

            _logger.LogInformation("成功取得 {Count} 個影片留言", comments.Count);
            return comments.Take(maxResults).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "取得影片留言時發生錯誤: {VideoId}", videoId);
            return comments;
        }
    }

    public async Task<List<SubtitleLanguageInfo>> GetAvailableSubtitlesAsync(string videoId, CancellationToken cancellationToken = default)
    {
        var subtitles = new List<SubtitleLanguageInfo>();

        try
        {
            _logger.LogDebug("正在取得影片字幕資訊: {VideoId}", videoId);

            var captionsRequest = _youTubeService.Captions.List("snippet", videoId);
            var captionsResponse = await captionsRequest.ExecuteAsync();

            foreach (var caption in captionsResponse.Items ?? new List<Google.Apis.YouTube.v3.Data.Caption>())
            {
                subtitles.Add(new SubtitleLanguageInfo
                {
                    LanguageCode = caption.Snippet?.Language ?? "",
                    LanguageName = caption.Snippet?.Name ?? "",
                    IsAutoGenerated = caption.Snippet?.TrackKind == "ASR" // ASR = Automatic Speech Recognition
                });
            }

            _logger.LogInformation("找到 {Count} 個可用字幕", subtitles.Count);
            return subtitles;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "取得影片字幕資訊時發生錯誤: {VideoId}", videoId);
            return subtitles;
        }
    }

    public async Task<List<string>> ExtractKeywordsFromVideoAsync(YouTubeVideoInfo videoInfo, CancellationToken cancellationToken = default)
    {
        var keywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        try
        {
            _logger.LogDebug("正在從影片資訊中萃取關鍵字: {VideoId}", videoInfo.Id);

            // 從標題萃取關鍵字
            if (!string.IsNullOrEmpty(videoInfo.Title))
            {
                var titleKeywords = ExtractKeywordsFromText(videoInfo.Title);
                foreach (var keyword in titleKeywords)
                {
                    keywords.Add(keyword);
                }
            }

            // 從描述萃取關鍵字
            if (!string.IsNullOrEmpty(videoInfo.Description))
            {
                var descKeywords = ExtractKeywordsFromText(videoInfo.Description);
                foreach (var keyword in descKeywords.Take(20)) // 限制描述關鍵字數量
                {
                    keywords.Add(keyword);
                }
            }

            // 從分類名稱添加關鍵字
            if (!string.IsNullOrEmpty(videoInfo.CategoryName))
            {
                keywords.Add(videoInfo.CategoryName);
            }

            // 從頻道名稱添加關鍵字
            if (!string.IsNullOrEmpty(videoInfo.Channel))
            {
                var channelKeywords = ExtractKeywordsFromText(videoInfo.Channel);
                foreach (var keyword in channelKeywords)
                {
                    keywords.Add(keyword);
                }
            }

            var result = keywords.Where(k => k.Length >= 2 && k.Length <= 50).ToList();
            _logger.LogInformation("從影片資訊中萃取到 {Count} 個關鍵字", result.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "萃取影片關鍵字時發生錯誤: {VideoId}", videoInfo.Id);
            return new List<string>();
        }
    }

    private List<string> ExtractKeywordsFromText(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return new List<string>();

        var keywords = new List<string>();
        
        // 簡單的關鍵字萃取邏輯
        var words = text.Split(new[] { ' ', '\n', '\r', '\t', ',', '.', '!', '?', ';', ':', '-', '_', '(', ')', '[', ']', '{', '}' }, 
                               StringSplitOptions.RemoveEmptyEntries);
        
        // 過濾停用詞和短詞
        var stopWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were",
            "a", "an", "this", "that", "these", "those", "i", "you", "he", "she", "it", "we", "they",
            "我", "你", "他", "她", "它", "我們", "你們", "他們", "的", "了", "在", "是", "有", "和", "與", "或",
            "不", "沒", "也", "都", "就", "會", "能", "可以", "這", "那", "個", "一", "二", "三"
        };

        foreach (var word in words)
        {
            var cleanWord = word.Trim();
            if (cleanWord.Length >= 2 && cleanWord.Length <= 30 && !stopWords.Contains(cleanWord))
            {
                keywords.Add(cleanWord);
            }
        }

        return keywords.Distinct().ToList();
    }

    public void Dispose()
    {
        _youTubeService?.Dispose();
    }
}