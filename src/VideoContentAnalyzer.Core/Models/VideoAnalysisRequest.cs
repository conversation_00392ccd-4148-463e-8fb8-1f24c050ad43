namespace VideoContentAnalyzer.Core.Models;

public class VideoAnalysisRequest
{
    public required string VideoPath { get; set; }
    public string? SubtitlePath { get; set; }
    public VideoAnalysisOptions Options { get; set; } = new();

    /// <summary>
    /// YouTube 影片元數據，當分析的影片來自 YouTube 時提供
    /// 用於增強 AI 分析的上下文資訊
    /// </summary>
    public YouTubeVideoInfo? YouTubeMetadata { get; set; }
}

public class VideoAnalysisOptions
{
    public int FrameExtractionIntervalSeconds { get; set; } = 5;
    public bool UseSceneChangeDetection { get; set; } = true;
    public bool GenerateSubtitlesIfMissing { get; set; } = true;
    public bool EnableTextRecognition { get; set; } = true;
    public bool EnablePlaceRecognition { get; set; } = true;
    public string OutputFormat { get; set; } = "json";
    public int MaxFramesPerVideo { get; set; } = 100;
}