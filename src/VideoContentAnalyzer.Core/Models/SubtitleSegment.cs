namespace VideoContentAnalyzer.Core.Models;

public class SubtitleSegment
{
    public TimeSpan StartTime { get; set; }
    public TimeSpan EndTime { get; set; }
    public required string Text { get; set; }
    public string Language { get; set; } = "unknown";
    public double Confidence { get; set; }
    public SubtitleAnalysis Analysis { get; set; } = new();
}

public class SubtitleAnalysis
{
    public string Sentiment { get; set; } = "neutral";
    public List<string> Keywords { get; set; } = [];
    public string Summary { get; set; } = string.Empty;
    public List<string> Topics { get; set; } = [];
    public bool IsDialog { get; set; }
    public string? Speaker { get; set; }
}