namespace VideoContentAnalyzer.Core.Models;

public class FrameAnalysis
{
    public TimeSpan Timestamp { get; set; }
    public string FramePath { get; set; } = string.Empty;
    public SceneDescription Scene { get; set; } = new();
    public List<DetectedText> DetectedTexts { get; set; } = [];
    public List<PlaceInfo> PlaceInfos { get; set; } = [];
    public double ConfidenceScore { get; set; }
    
    // 新增：分析耗時資訊
    public TimeSpan AnalysisDuration { get; set; }
    public DateTime AnalysisStartTime { get; set; }
    public DateTime AnalysisEndTime { get; set; }
}

public class SceneDescription
{
    public string MainDescription { get; set; } = string.Empty;
    public string CuisineType { get; set; } = string.Empty; // 飲食類型：和食、烏龍麵、甜點等
    public string RestaurantCategory { get; set; } = string.Empty; // 餐廳分類：麵食店、甜點店、咖啡廳等
    public List<string> Activities { get; set; } = [];
    public List<DetectedObject> Objects { get; set; } = [];
    public string Setting { get; set; } = string.Empty;
    public List<string> Colors { get; set; } = [];
    public string Mood { get; set; } = string.Empty;
    public List<string> VisibleTexts { get; set; } = []; // 所有可見文字的快速列表
}

public class AnalysisTimingInfo
{
    public TimeSpan Duration { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Stage { get; set; } = string.Empty; // "FrameAnalysis", "TextExtraction", etc.
}

public class PerformanceMetrics
{
    public TimeSpan TotalAnalysisTime { get; set; }
    public TimeSpan AverageFrameAnalysisTime { get; set; }
    public TimeSpan FastestFrameAnalysisTime { get; set; }
    public TimeSpan SlowestFrameAnalysisTime { get; set; }
    public int TotalFramesAnalyzed { get; set; }
    public int SuccessfulAnalyses { get; set; }
    public int FailedAnalyses { get; set; }
    public List<AnalysisTimingInfo> DetailedTimings { get; set; } = [];
}

public class DetectedObject
{
    public required string Name { get; set; }
    public double Confidence { get; set; }
    public BoundingBox? BoundingBox { get; set; }
}

public class DetectedText
{
    public required string Text { get; set; }
    public double Confidence { get; set; }
    public BoundingBox? BoundingBox { get; set; }
    public string Language { get; set; } = "unknown";
    public string? Location { get; set; } // 文字位置描述 (如: "招牌", "字幕", "海報")
}

public class PlaceInfo
{
    public string? Name { get; set; } // 餐廳/景點名稱
    public string? Address { get; set; } // 完整地址
    public string? Phone { get; set; } // 電話號碼
    public string? Website { get; set; } // 網址
    public string? BusinessHours { get; set; } // 營業時間
    public string? Category { get; set; } // 類別 (餐廳/景點/商店等)
    public string? Description { get; set; } // 描述
    public double Confidence { get; set; } // 識別信心度
    public List<string> OriginalTexts { get; set; } = []; // 原始文字來源

    // Google Places API 特有欄位
    public string? GooglePlaceId { get; set; } // Google Place ID
    public double? GoogleRating { get; set; } // Google 評分 (1-5)
    public int? GooglePriceLevel { get; set; } // Google 價格等級 (0-4)
    public int? GoogleUserRatingsTotal { get; set; } // Google 評論總數
    public List<string> GooglePhotoReferences { get; set; } = []; // Google 照片參考
}

public class BoundingBox
{
    public int X { get; set; }
    public int Y { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
}