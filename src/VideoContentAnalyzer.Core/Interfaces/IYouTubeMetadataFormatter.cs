using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Interfaces;

/// <summary>
/// YouTube 元數據格式化服務介面
/// 負責將 YouTubeVideoInfo 格式化為適合 AI 理解的文字描述
/// </summary>
public interface IYouTubeMetadataFormatter
{
    /// <summary>
    /// 將 YouTube 影片元數據格式化為 AI 分析的上下文文字
    /// </summary>
    /// <param name="videoInfo">YouTube 影片資訊</param>
    /// <returns>格式化的上下文文字，如果 videoInfo 為 null 則返回空字串</returns>
    string FormatMetadataForAI(YouTubeVideoInfo? videoInfo);
    
    /// <summary>
    /// 格式化影片背景資訊（標題、描述、分類）
    /// </summary>
    /// <param name="videoInfo">YouTube 影片資訊</param>
    /// <returns>影片背景資訊文字</returns>
    string FormatVideoBackground(YouTubeVideoInfo videoInfo);
    
    /// <summary>
    /// 格式化頻道資訊
    /// </summary>
    /// <param name="videoInfo">YouTube 影片資訊</param>
    /// <returns>頻道資訊文字</returns>
    string FormatChannelInfo(YouTubeVideoInfo videoInfo);
    
    /// <summary>
    /// 格式化地理位置資訊
    /// </summary>
    /// <param name="videoInfo">YouTube 影片資訊</param>
    /// <returns>地理位置資訊文字</returns>
    string FormatLocationInfo(YouTubeVideoInfo videoInfo);
    
    /// <summary>
    /// 格式化標籤和關鍵字資訊
    /// </summary>
    /// <param name="videoInfo">YouTube 影片資訊</param>
    /// <returns>標籤和關鍵字資訊文字</returns>
    string FormatTagsAndKeywords(YouTubeVideoInfo videoInfo);
    
    /// <summary>
    /// 格式化統計資訊（觀看數、按讚數等）
    /// </summary>
    /// <param name="videoInfo">YouTube 影片資訊</param>
    /// <returns>統計資訊文字</returns>
    string FormatStatistics(YouTubeVideoInfo videoInfo);
}
