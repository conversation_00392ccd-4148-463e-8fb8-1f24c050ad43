using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Interfaces;

public interface IVideoFrameExtractor
{
    Task<List<ExtractedFrame>> ExtractFramesAsync(string videoPath, VideoAnalysisOptions options, CancellationToken cancellationToken = default);
    Task<TimeSpan> GetVideoDurationAsync(string videoPath, CancellationToken cancellationToken = default);
    Task<List<TimeSpan>> DetectSceneChangesAsync(string videoPath, CancellationToken cancellationToken = default);
}

public class ExtractedFrame
{
    public TimeSpan Timestamp { get; set; }
    public required string FramePath { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public long FileSizeBytes { get; set; }
}