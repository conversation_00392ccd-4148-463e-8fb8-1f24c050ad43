using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Interfaces;

public interface IVideoAnalyzer
{
    Task<VideoAnalysisResult> AnalyzeVideoAsync(VideoAnalysisRequest request, CancellationToken cancellationToken = default);
    Task<VideoAnalysisResult> AnalyzeVideoAsync(VideoAnalysisRequest request, IProgress<AnalysisProgress>? progress = null, CancellationToken cancellationToken = default);
}

public class AnalysisProgress
{
    public string CurrentStage { get; set; } = string.Empty;
    public double ProgressPercentage { get; set; }
    public string Message { get; set; } = string.Empty;
    public TimeSpan Elapsed { get; set; }
    public TimeSpan? EstimatedRemaining { get; set; }
}