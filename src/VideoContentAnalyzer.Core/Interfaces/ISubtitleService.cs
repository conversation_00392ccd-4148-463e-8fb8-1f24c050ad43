using VideoContentAnalyzer.Core.Models;

namespace VideoContentAnalyzer.Core.Interfaces;

public interface ISubtitleService
{
    Task<List<SubtitleSegment>> ParseSubtitleFileAsync(string subtitlePath, CancellationToken cancellationToken = default);
    Task<List<SubtitleSegment>> GenerateSubtitlesAsync(string videoPath, CancellationToken cancellationToken = default);
    Task<bool> IsSubtitleFileValidAsync(string subtitlePath, CancellationToken cancellationToken = default);
    Task<string> DetectSubtitleFormatAsync(string subtitlePath, CancellationToken cancellationToken = default);
}

public interface IWhisperService
{
    Task<List<SubtitleSegment>> TranscribeAudioAsync(string audioPath, CancellationToken cancellationToken = default);
    Task<string> ExtractAudioFromVideoAsync(string videoPath, CancellationToken cancellationToken = default);
    Task<bool> IsWhisperAvailableAsync(CancellationToken cancellationToken = default);
}