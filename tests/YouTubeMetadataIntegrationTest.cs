using VideoContentAnalyzer.Core.Models;
using VideoContentAnalyzer.Infrastructure.Services;
using VideoContentAnalyzer.Infrastructure.AI;
using Microsoft.Extensions.Logging;

namespace VideoContentAnalyzer.Tests;

/// <summary>
/// 測試 YouTube 元數據整合到 AI 分析流程的功能
/// </summary>
public class YouTubeMetadataIntegrationTest
{
    /// <summary>
    /// 測試 AI 分析服務是否正確使用 YouTube 元數據
    /// </summary>
    public static void TestAIAnalysisWithYouTubeMetadata()
    {
        Console.WriteLine("=== YouTube 元數據整合測試 ===");
        
        // 創建測試用的 YouTube 影片資訊（基於實際的 <PERSON> 影片）
        var youtubeMetadata = new YouTubeVideoInfo
        {
            Id = "dQw4w9WgXcQ",
            Title = "<PERSON> - Never Gonna Give You Up (Official Video) (4K Remaster)",
            Url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            Description = "The official video for \"Never Gonna Give You Up\" by <PERSON>. Taken from the album 'Whenever You Need Somebody' – deluxe 2CD and digital deluxe out 6th May 2022.",
            Channel = "<PERSON>",
            CategoryName = "Music",
            DefaultLanguage = "en",
            ViewCount = 1687124366,
            LikeCount = 15000000,
            CommentCount = 2500000,
            UploadDate = DateTime.Parse("2009-10-25"),
            Tags = new List<string> { "Rick Astley", "Never Gonna Give You Up", "80s", "pop", "music video", "official" },
            ExtractedKeywords = new List<string> { "Rick Astley", "Never Gonna Give You Up", "80s music", "pop music", "official video", "remaster" },
            ChannelInfo = new YouTubeChannelInfo
            {
                Id = "UCuAXFkgsw1L7xaCfnd5JJOw",
                Title = "Rick Astley",
                SubscriberCount = 4390000,
                Country = "GB",
                Description = "The official Rick Astley YouTube channel"
            }
        };

        // 創建 YouTubeMetadataFormatter
        var formatter = new YouTubeMetadataFormatter();
        
        // 測試元數據格式化
        var formattedMetadata = formatter.FormatMetadataForAI(youtubeMetadata);
        
        Console.WriteLine("✅ 成功格式化 YouTube 元數據");
        Console.WriteLine($"格式化後的元數據長度: {formattedMetadata.Length} 字元");
        
        // 驗證格式化結果包含關鍵資訊
        var expectedContent = new[]
        {
            "Rick Astley",
            "Never Gonna Give You Up",
            "Music",
            "80s",
            "1.7B", // 觀看次數格式化
            "4.4M"  // 訂閱者數格式化
        };
        
        foreach (var content in expectedContent)
        {
            if (!formattedMetadata.Contains(content))
            {
                throw new Exception($"格式化結果應包含: {content}");
            }
        }
        
        Console.WriteLine("✅ 格式化結果包含所有預期內容");
        
        // 測試 AI prompt 構建（模擬）
        var mockPromptWithMetadata = $"""
            {formattedMetadata}

            請仔細分析這張影片截圖，**專門尋找和提取餐廳名稱及地址資訊**。請用繁體中文回應。

            **根據上述影片背景資訊，請特別注意：**
            - 如果影片標題或描述中提到特定地區，請優先考慮該地區的餐廳和地點
            - 如果影片標籤包含特定料理類型，請在分析時重點關注相關的餐廳類型
            """;
        
        // 驗證 prompt 包含元數據
        if (!mockPromptWithMetadata.Contains("Rick Astley"))
        {
            throw new Exception("AI prompt 應包含 YouTube 元數據");
        }
        
        Console.WriteLine("✅ AI prompt 成功整合 YouTube 元數據");
        
        // 測試不同的元數據組合
        TestPartialMetadata();
        TestEmptyMetadata();
        
        Console.WriteLine("🎉 YouTube 元數據整合測試全部通過！");
    }
    
    /// <summary>
    /// 測試部分元數據的處理
    /// </summary>
    private static void TestPartialMetadata()
    {
        var partialMetadata = new YouTubeVideoInfo
        {
            Id = "test123",
            Title = "測試影片",
            Url = "https://www.youtube.com/watch?v=test123",
            // 只有基本資訊
        };
        
        var formatter = new YouTubeMetadataFormatter();
        var result = formatter.FormatMetadataForAI(partialMetadata);
        
        if (string.IsNullOrEmpty(result))
        {
            throw new Exception("部分元數據也應能正常格式化");
        }
        
        Console.WriteLine("✅ 部分元數據處理測試通過");
    }
    
    /// <summary>
    /// 測試空元數據的處理
    /// </summary>
    private static void TestEmptyMetadata()
    {
        var formatter = new YouTubeMetadataFormatter();
        var result = formatter.FormatMetadataForAI(null);
        
        if (!string.IsNullOrEmpty(result))
        {
            throw new Exception("空元數據應返回空字串");
        }
        
        Console.WriteLine("✅ 空元數據處理測試通過");
    }
    
    /// <summary>
    /// 測試 VideoAnalysisRequest 的 YouTube 元數據傳遞
    /// </summary>
    public static void TestVideoAnalysisRequestIntegration()
    {
        Console.WriteLine("\n=== VideoAnalysisRequest 整合測試 ===");
        
        var youtubeMetadata = new YouTubeVideoInfo
        {
            Id = "test456",
            Title = "整合測試影片",
            Url = "https://www.youtube.com/watch?v=test456"
        };
        
        // 創建包含 YouTube 元數據的分析請求
        var request = new VideoAnalysisRequest
        {
            VideoPath = "/path/to/test/video.mp4",
            YouTubeMetadata = youtubeMetadata,
            Options = new VideoAnalysisOptions
            {
                MaxFramesPerVideo = 5,
                EnableTextRecognition = true
            }
        };
        
        // 驗證元數據正確傳遞
        if (request.YouTubeMetadata == null)
        {
            throw new Exception("VideoAnalysisRequest 應包含 YouTube 元數據");
        }
        
        if (request.YouTubeMetadata.Title != "整合測試影片")
        {
            throw new Exception("YouTube 元數據應正確傳遞");
        }
        
        Console.WriteLine("✅ VideoAnalysisRequest 成功整合 YouTube 元數據");
        Console.WriteLine("🎉 VideoAnalysisRequest 整合測試通過！");
    }
    
    /// <summary>
    /// 運行所有整合測試
    /// </summary>
    public static void RunAllIntegrationTests()
    {
        try
        {
            Console.WriteLine("開始 YouTube 元數據整合測試...\n");
            
            TestAIAnalysisWithYouTubeMetadata();
            TestVideoAnalysisRequestIntegration();
            
            Console.WriteLine("\n🎉 所有整合測試通過！YouTube 元數據整合功能完全正常。");
            Console.WriteLine("\n📋 測試摘要：");
            Console.WriteLine("✅ YouTube 元數據格式化");
            Console.WriteLine("✅ AI prompt 整合");
            Console.WriteLine("✅ VideoAnalysisRequest 傳遞");
            Console.WriteLine("✅ 部分/空元數據處理");
            Console.WriteLine("✅ 錯誤處理");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 整合測試失敗：{ex.Message}");
            throw;
        }
    }
}

/// <summary>
/// 整合測試運行器
/// </summary>
public class IntegrationTestProgram
{
    public static void Main(string[] args)
    {
        YouTubeMetadataIntegrationTest.RunAllIntegrationTests();
    }
}
