using VideoContentAnalyzer.Core.Models;
using VideoContentAnalyzer.Infrastructure.Services;

namespace VideoContentAnalyzer.Tests;

/// <summary>
/// 測試 YouTubeMetadataFormatter 的功能
/// </summary>
public class YouTubeMetadataFormatterTests
{
    private readonly YouTubeMetadataFormatter _formatter;

    public YouTubeMetadataFormatterTests()
    {
        _formatter = new YouTubeMetadataFormatter();
    }

    /// <summary>
    /// 測試基本的元數據格式化功能
    /// </summary>
    public void TestBasicMetadataFormatting()
    {
        // 創建測試用的 YouTube 影片資訊
        var videoInfo = new YouTubeVideoInfo
        {
            Id = "test123",
            Title = "東京美食探索 - 新宿拉麵店巡禮",
            Url = "https://www.youtube.com/watch?v=test123",
            Description = "這次我們來到東京新宿，探索當地最受歡迎的拉麵店。從傳統的豚骨拉麵到創新的味噌拉麵，一起品嚐東京的拉麵文化。",
            Channel = "美食探險家",
            CategoryName = "旅遊與活動",
            DefaultLanguage = "ja",
            ViewCount = 150000,
            LikeCount = 8500,
            CommentCount = 320,
            UploadDate = DateTime.Parse("2024-01-15"),
            Tags = new List<string> { "拉麵", "東京", "美食", "日本料理", "新宿" },
            ExtractedKeywords = new List<string> { "豚骨拉麵", "味噌拉麵", "東京美食", "新宿" },
            DetectedPlaces = new List<PlaceReference>
            {
                new PlaceReference
                {
                    Name = "一蘭拉麵 新宿店",
                    FormattedAddress = "東京都新宿區新宿3-34-11",
                    Types = new List<string> { "restaurant", "food" },
                    DetectionSource = "title",
                    ConfidenceScore = 0.85
                }
            },
            ChannelInfo = new YouTubeChannelInfo
            {
                Id = "channel123",
                Title = "美食探險家",
                SubscriberCount = 250000,
                Country = "JP"
            }
        };

        // 測試格式化
        var result = _formatter.FormatMetadataForAI(videoInfo);

        // 驗證結果
        Console.WriteLine("=== YouTube 元數據格式化測試結果 ===");
        Console.WriteLine(result);
        Console.WriteLine("=====================================");

        // 基本驗證
        if (string.IsNullOrEmpty(result))
        {
            throw new Exception("格式化結果不應為空");
        }

        if (!result.Contains("東京美食探索"))
        {
            throw new Exception("應包含影片標題");
        }

        if (!result.Contains("拉麵"))
        {
            throw new Exception("應包含標籤資訊");
        }

        if (!result.Contains("一蘭拉麵"))
        {
            throw new Exception("應包含檢測到的地點資訊");
        }

        if (!result.Contains("美食探險家"))
        {
            throw new Exception("應包含頻道資訊");
        }

        Console.WriteLine("✅ 基本元數據格式化測試通過");
    }

    /// <summary>
    /// 測試空元數據的處理
    /// </summary>
    public void TestNullMetadataHandling()
    {
        var result = _formatter.FormatMetadataForAI(null);
        
        if (!string.IsNullOrEmpty(result))
        {
            throw new Exception("空元數據應返回空字串");
        }

        Console.WriteLine("✅ 空元數據處理測試通過");
    }

    /// <summary>
    /// 測試部分元數據的格式化
    /// </summary>
    public void TestPartialMetadataFormatting()
    {
        var videoInfo = new YouTubeVideoInfo
        {
            Id = "test456",
            Title = "簡單測試影片",
            Url = "https://www.youtube.com/watch?v=test456",
            // 只有基本資訊，沒有標籤、地點等
        };

        var result = _formatter.FormatMetadataForAI(videoInfo);

        if (string.IsNullOrEmpty(result))
        {
            throw new Exception("部分元數據也應能正常格式化");
        }

        if (!result.Contains("簡單測試影片"))
        {
            throw new Exception("應包含基本的標題資訊");
        }

        Console.WriteLine("✅ 部分元數據格式化測試通過");
    }

    /// <summary>
    /// 運行所有測試
    /// </summary>
    public static void RunAllTests()
    {
        var tests = new YouTubeMetadataFormatterTests();
        
        try
        {
            Console.WriteLine("開始 YouTube 元數據格式化測試...\n");
            
            tests.TestBasicMetadataFormatting();
            tests.TestNullMetadataHandling();
            tests.TestPartialMetadataFormatting();
            
            Console.WriteLine("\n🎉 所有測試通過！YouTube 元數據整合功能正常運作。");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 測試失敗：{ex.Message}");
            throw;
        }
    }
}

/// <summary>
/// 簡單的測試運行器
/// </summary>
public class Program
{
    public static void Main(string[] args)
    {
        YouTubeMetadataFormatterTests.RunAllTests();
    }
}
