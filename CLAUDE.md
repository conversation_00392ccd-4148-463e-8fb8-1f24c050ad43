# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build & Restore
```bash
dotnet restore                                    # 恢復專案相依性
dotnet build                                     # 建置整個解決方案
dotnet build --configuration Release            # Release 模式建置
```

### Run Application
```bash
# 基本用法 - 分析影片
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/video.mp4

# 使用字幕檔案
dotnet run --project src/VideoContentAnalyzer.Console -- /path/to/video.mp4 -s /path/to/subtitle.srt

# YouTube 影片下載和分析
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube https://www.youtube.com/watch?v=VIDEO_ID
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube VIDEO_ID

# 保留下載的 YouTube 影片檔案
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube VIDEO_ID --keep-download

# 測試 LM Studio API 連接
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api

# 指定輸出格式和參數
dotnet run --project src/VideoContentAnalyzer.Console -- \
  /path/to/video.mp4 \
  --interval 10 \
  --max-frames 30 \
  --output markdown \
  --verbose
```

### Testing
目前專案沒有正式的測試專案，可使用：
```bash
# 測試 LM Studio 連接
dotnet run --project src/VideoContentAnalyzer.Console -- --test-api

# 使用 Python 測試腳本（如果存在）
python3 test-lm-studio.py

# 測試基本功能（使用 YouTube 範例）
dotnet run --project src/VideoContentAnalyzer.Console -- --youtube feT2fQ7hR-I --verbose --max-frames 1
```

## Architecture Overview

這是一個基於 .NET 9 的影片內容分析工具，採用 Clean Architecture 設計，分為三個主要層級：

### Project Structure
```
src/
├── VideoContentAnalyzer.Core/        # 領域層 - 業務邏輯和核心模型
│   ├── Models/                      # 資料模型（VideoAnalysisResult, FrameAnalysis等）
│   ├── Interfaces/                  # 服務介面定義
│   └── Services/                    # 核心業務服務（VideoAnalyzer）
├── VideoContentAnalyzer.Infrastructure/ # 基礎設施層 - 外部服務整合
│   ├── AI/                         # LM Studio 和 Whisper AI 服務
│   ├── Media/                      # FFmpeg 影片處理服務
│   └── Subtitle/                   # 字幕解析和生成服務
└── VideoContentAnalyzer.Console/   # 應用層 - CLI 介面和 DI 配置
```

### Core Dependencies
- **FFMpegCore**: 影片處理和幀擷取
- **Whisper.net**: 語音轉文字（字幕生成）
- **RestSharp**: HTTP 客戶端（LM Studio API 通訊）
- **SubtitlesParser**: 字幕檔案解析
- **SixLabors.ImageSharp**: 圖像處理
- **Spectre.Console**: 命令列介面和進度顯示

### Key Workflows
1. **影片分析流程**: VideoAnalyzer.cs:31 - 主要分析流程，包含幀擷取、AI 分析、字幕處理和摘要生成
2. **依賴注入配置**: Program.cs:163 - 服務註冊和配置綁定
3. **進度報告機制**: IVideoAnalyzer 支援 IProgress<AnalysisProgress> 用於追蹤分析進度

### Configuration
- 使用 YAML 配置檔案（src/VideoContentAnalyzer.Console/appsettings.yaml）
- 主要配置區塊：LMStudio、Whisper、FrameExtraction、PlaceRecognition、YouTubeApi、YouTubeDownload、Advanced
- 支援環境變數覆寫配置（如 YOUTUBE_API_KEY）
- 預設使用 data/ 目錄儲存下載檔案和截圖

### External Dependencies
- **LM Studio**: 本地運行的 AI 模型伺服器（視覺語言模型）
- **FFmpeg**: 系統層級的影片處理工具
- **Whisper Models**: 語音識別模型檔案（需要下載）
- **yt-dlp**: YouTube 影片下載工具（需要安裝）

### Place Recognition (Restaurant/Attraction Analysis)
- **PlaceRecognition 配置區塊**: 專門針對餐廳和景點識別優化
- **多語言文字識別**: 重點支援中文（繁簡）、日文、韓文文字識別
- **結構化場所資訊提取**: 自動識別餐廳名稱、地址、營業時間、聯絡方式
- **AI 提示詞優化**: AIAnalysisService 包含三個專門方法：
  - `AnalyzeFrameAsync`: 優先識別場所相關文字和資訊
  - `ExtractTextFromFrameAsync`: 針對亞洲文字和招牌文字優化
  - `ExtractPlaceInfoAsync`: 專門提取結構化餐廳/景點資料
- **智慧摘要生成**: 影片摘要會優先列出識別到的餐廳和景點清單

### YouTube Download Integration
- **YouTubeDownloadService**: 使用 yt-dlp 下載 YouTube 影片
- **多種輸入格式**: 支援完整 YouTube URL 或 11 位 Video ID
- **品質控制**: 可配置影片品質（720p、1080p、best、worst）
- **字幕下載**: 自動下載多語言字幕（中文、英文、日文、韓文）
- **自動清理**: 分析完成後可選擇保留或刪除下載檔案
- **進度顯示**: 即時顯示下載進度和檔案資訊

### Error Handling
採用結構化日誌和例外處理，核心服務會記錄錯誤但繼續處理其他項目，最終在摘要中標註失敗的部分。