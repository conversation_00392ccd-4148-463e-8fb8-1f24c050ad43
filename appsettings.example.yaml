# Video Content Analyzer Configuration Example
# 複製此檔案為 appsettings.yaml 並根據需要修改設定

Logging:
  LogLevel:
    Default: Information
    Microsoft: Warning
    Microsoft.Hosting.Lifetime: Information

# LM Studio 設定
LMStudio:
  # LM Studio API 端點
  BaseUrl: http://localhost:1234
  
  # 視覺模型名稱 (如 LLaVA, Qwen-VL 等)
  VisionModel: llava-1.5-7b-q4_0
  
  # 文字模型名稱
  TextModel: llama-2-7b-chat-q4_0
  
  # 最大 Token 數量
  MaxTokens: 1000
  
  # 溫度設定 (0.0-1.0)
  Temperature: 0.7
  
  # API 超時時間（秒）
  TimeoutSeconds: 120
  
  # 圖片最大尺寸（像素）
  MaxImageSize: 1024
  
  # JPEG 圖片品質 (1-100)
  ImageQuality: 85

# Whisper 語音識別設定
Whisper:
  # Whisper 模型檔案路徑
  ModelPath: ~/.whisper/ggml-base.bin
  
  # 模型大小 (tiny, base, small, medium, large)
  ModelSize: base
  
  # 語言設定 (auto 為自動偵測)
  Language: auto
  
  # 是否翻譯為英文
  Translate: false
  
  # 使用的執行緒數量
  Threads: 4

# 截圖擷取設定
FrameExtraction:
  # 截圖儲存路徑配置
  OutputDirectory: ./frames              # 當前目錄下的 frames 資料夾
  # OutputDirectory: ~/VideoAnalysis     # 使用者目錄下的 VideoAnalysis
  # OutputDirectory: /tmp/video-frames   # 系統臨時目錄
  # OutputDirectory: C:\VideoFrames      # Windows 絕對路徑
  
  # 檔案名稱是否包含時間戳
  UseTimestampInFilename: true
  
  # 截圖圖片格式 (jpg, png, bmp, webp)
  ImageFormat: jpg
  
  # 圖片品質設定 (1-100，僅適用於 JPEG)
  ImageQuality: 90
  
  # 分析完成後是否保留截圖檔案
  KeepFrames: false                      # false = 自動清理，true = 保留
  
  # 是否為每個影片建立獨立子目錄
  CreateSubDirectoryPerVideo: true
  
  # 子目錄命名格式（支援變數）
  # 可用變數: {videoName}, {timestamp:格式}
  SubDirectoryNameFormat: "{videoName}_{timestamp:yyyyMMdd_HHmmss}"

# 進階設定
Advanced:
  # 最大並行處理的幀數
  MaxConcurrentFrameAnalysis: 3
  
  # 是否啟用結果快取
  EnableResultCache: true
  
  # 快取檔案儲存目錄
  CacheDirectory: ./cache
  
  # 錯誤重試次數
  MaxRetryAttempts: 3
  
  # 重試延遲時間（秒）
  RetryDelaySeconds: 2